<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Oil & Gas Well Documentation System</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, Arial, sans-serif;
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            min-height: 100vh;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .sidebar h2 {
            color: #1a365d;
            margin-bottom: 25px;
            font-size: 1.4em;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .nav-item:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .nav-item.completed {
            border-color: #48bb78;
        }

        .nav-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e2e8f0;
            margin-right: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .nav-item.active .nav-number {
            background: white;
            color: #3182ce;
        }

        .nav-item.completed .nav-number {
            background: #48bb78;
            color: white;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            background: #f7fafc;
        }

        .nav-item.active .nav-badge {
            background: rgba(255, 255, 255, 0.3);
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: #1a365d;
            margin-bottom: 10px;
            font-size: 2em;
        }

        .page-header p {
            color: #718096;
        }

        .form-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h3 {
            color: #1a365d;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
            font-size: 1.2em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #4a5568;
            font-size: 14px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #cbd5e0;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #f7fafc;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #2d3748;
            border-bottom: 2px solid #e2e8f0;
        }

        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }

        .data-table tr:hover td {
            background: #f7fafc;
        }

        .data-table input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 13px;
        }

        .data-table input:focus {
            outline: none;
            border-color: #4299e1;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn-add {
            background: #4299e1;
            color: white;
            padding: 8px 16px;
            font-size: 13px;
        }

        .btn-remove {
            background: #fc8181;
            color: white;
            padding: 4px 10px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .action-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .final-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .progress-indicator {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-info {
            background: #bee3f8;
            color: #2c5282;
            border: 1px solid #90cdf4;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #718096;
        }

        .empty-state h3 {
            color: #4a5568;
            margin-bottom: 10px;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-card {
            animation: slideIn 0.3s ease;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-complete {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-incomplete {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-in-progress {
            background: #feebc8;
            color: #7c2d12;
        }

        .upload-section {
            background: #f8fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .upload-section.processing {
            border-color: #f6ad55;
            background: #fffaf0;
        }

        .upload-section.success {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .upload-input {
            display: none;
        }

        .upload-button {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.3);
        }

        .upload-text {
            color: #718096;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .file-info {
            background: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-name {
            font-weight: 500;
            color: #2d3748;
        }

        .file-size {
            color: #718096;
            font-size: 12px;
        }

        .processing-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .extraction-status {
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            font-weight: 500;
        }

        .extraction-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .extraction-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .extraction-processing {
            background: #feebc8;
            color: #7c2d12;
            border: 1px solid #f6ad55;
        }

        .api-config {
            background: #fff5f5;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .api-config h4 {
            color: #742a2a;
            margin-bottom: 10px;
        }

        .auto-fill-button {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
            margin-left: 10px;
        }

        .auto-fill-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(237, 137, 54, 0.3);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;

        // Main Application Component
        function WellDocumentationSystem() {
            const [currentPage, setCurrentPage] = useState(0);
            const [geminiApiKey, setGeminiApiKey] = useState('');
            const [uploadedFiles, setUploadedFiles] = useState({});
            const [processingStatus, setProcessingStatus] = useState({});
            const [extractionResults, setExtractionResults] = useState({});
            const [formData, setFormData] = useState(() => {
                // Initialize with empty data structure for all forms
                return {
                    trajectory: {
                        sequenceNo: '',
                        field: '',
                        well: '',
                        serviceLocation: '',
                        reservoirLocation: '',
                        azimuth: '',
                        utmZone: '',
                        easting: '',
                        northing: '',
                        latitude: '',
                        longitude: '',
                        rigNumber: '',
                        groundLevel: '',
                        rtkb: '',
                        departure: { feet: '', meters: '', eastingLP: '', northingLP: '', td: '' },
                        elevations: []
                    },
                    geological: {
                        wellName: '',
                        productionType: '',
                        formationTops: [],
                        nearbyWells: [],
                        plan: { tvdss: '', tvdrtkb: '', dap: '', remarks: '' }
                    },
                    location: {
                        subject: '',
                        subLocation: '',
                        well: '',
                        objective: '',
                        sequence: '',
                        number: '',
                        from: '',
                        date: '',
                        field: '',
                        wellNumber: '',
                        rig: '',
                        easting: '',
                        northing: '',
                        elevation: { gl: '', rtkb: '' },
                        latitude: '',
                        longitude: '',
                        utmZone: '',
                        datum: ''
                    },
                    prognosis: {
                        wellName: '',
                        rigName: '',
                        wellType: '',
                        prognosisStatus: '',
                        jobSequence: '',
                        field: '',
                        orientation: '',
                        targetedZones: '',
                        jobDescription: '',
                        budgetCategory: '',
                        basicDuration: '',
                        totalDuration: '',
                        expectedDate: '',
                        dwsRate: { ss: '', ls: '', st: '' },
                        prognosisRate: { ss: '', ls: '', st: '' },
                        waterInjection: { ss: '', ls: '', st: '' },
                        gasInjection: { ss: '', ls: '', st: '' },
                        controlPoints: [],
                        cuttings: [],
                        coring: [],
                        logging: [],
                        testing: []
                    },
                    completion: {
                        date: '',
                        completionType: '',
                        completionCode: '',
                        stopDate: '',
                        rig: '',
                        well: '',
                        casingSize: '',
                        casingWeight: '',
                        tieBack: 'No',
                        wrapOver: 'No',
                        tubingThread: '',
                        lowerCompletion: [],
                        upperCompletion: []
                    },
                    tubular: {
                        wellNumber: '',
                        field: '',
                        rig: '',
                        wellType: '',
                        adminAssets: '',
                        completionCode: '',
                        casingDesign: '',
                        additionalCompletion: '',
                        tubulars: []
                    },
                    wellInfo: {
                        rig: '',
                        spotDate: '',
                        well: '',
                        wellType: '',
                        expectedSpotDate: '',
                        productionTest: 'No',
                        formationFluidType: '',
                        casingDesign: '',
                        contingencyPlan: '',
                        specialRequirements: '',
                        sapReported: 'No',
                        sapCommitteeWell: 'No'
                    },
                    wellhead: {
                        wellNumber: '',
                        serviceLocation: '',
                        rate: '',
                        lastWorkOrder: '',
                        whInstallationDate: '',
                        wellObjective: '',
                        whCode: '',
                        expectedSpotDate: '',
                        equipment: []
                    }
                };
            });

            const [pageStatus, setPageStatus] = useState({
                trajectory: false,
                geological: false,
                location: false,
                prognosis: false,
                completion: false,
                tubular: false,
                wellInfo: false,
                wellhead: false
            });

            const pages = [
                { id: 'trajectory', title: 'Well Trajectory', type: 'PDF' },
                { id: 'geological', title: 'Geological Prediction', type: 'Excel' },
                { id: 'location', title: 'Location Memo', type: 'PDF' },
                { id: 'prognosis', title: 'Prognosis', type: 'PDF' },
                { id: 'completion', title: 'Completion Allocation', type: 'Excel' },
                { id: 'tubular', title: 'Tubular Allocation', type: 'Excel' },
                { id: 'wellInfo', title: 'Well Information', type: 'Excel' },
                { id: 'wellhead', title: 'Wellhead Allocation', type: 'Excel' }
            ];

            // Update form data
            const updateFormData = useCallback((pageId, updates) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        ...updates
                    }
                }));
            }, []);

            // Add table row
            const addTableRow = useCallback((pageId, tableName) => {
                const newRow = getEmptyRow(tableName);
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        [tableName]: [...(prev[pageId][tableName] || []), newRow]
                    }
                }));
            }, []);

            // Remove table row
            const removeTableRow = useCallback((pageId, tableName, index) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        [tableName]: prev[pageId][tableName].filter((_, i) => i !== index)
                    }
                }));
            }, []);

            // Update table row
            const updateTableRow = useCallback((pageId, tableName, index, field, value) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        [tableName]: prev[pageId][tableName].map((row, i) => 
                            i === index ? { ...row, [field]: value } : row
                        )
                    }
                }));
            }, []);

            // Handle file upload
            const handleFileUpload = useCallback(async (pageId, file) => {
                setUploadedFiles(prev => ({ ...prev, [pageId]: file }));
                setProcessingStatus(prev => ({ ...prev, [pageId]: 'processing' }));
                
                try {
                    await processDocumentWithGemini(pageId, file);
                } catch (error) {
                    console.error('Document processing failed:', error);
                    setProcessingStatus(prev => ({ ...prev, [pageId]: 'error' }));
                }
            }, []);

            // Process document with Gemini API
            const processDocumentWithGemini = async (pageId, file) => {
                if (!geminiApiKey) {
                    alert('Please enter your Gemini API key first');
                    setProcessingStatus(prev => ({ ...prev, [pageId]: 'error' }));
                    return;
                }

                try {
                    let documentContent = '';
                    
                    // Extract content based on file type
                    if (file.type === 'application/pdf') {
                        documentContent = await extractPDFText(file);
                    } else if (file.type.includes('sheet') || file.type.includes('excel')) {
                        documentContent = await extractExcelText(file);
                    } else if (file.type.startsWith('image/')) {
                        documentContent = await extractImageText(file);
                    } else {
                        documentContent = await file.text();
                    }

                    // Get field definitions for the current page
                    const fieldDefinitions = getFieldDefinitions(pageId);
                    
                    // Call Gemini API
                    const extractedData = await callGeminiAPI(documentContent, fieldDefinitions, pageId);
                    
                    // Auto-fill the form
                    if (extractedData) {
                        updateFormData(pageId, extractedData);
                        setExtractionResults(prev => ({ ...prev, [pageId]: extractedData }));
                        setProcessingStatus(prev => ({ ...prev, [pageId]: 'success' }));
                    }
                } catch (error) {
                    console.error('Processing error:', error);
                    setProcessingStatus(prev => ({ ...prev, [pageId]: 'error' }));
                }
            };

            // Extract text from PDF
            const extractPDFText = async (file) => {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const pdf = await PDFLib.PDFDocument.load(arrayBuffer);
                    const pages = pdf.getPages();
                    let text = '';
                    
                    // For now, return a placeholder since PDF text extraction is complex
                    // In a real implementation, you'd use a proper PDF text extraction library
                    return `PDF file uploaded: ${file.name}. Please use Gemini API to extract text content.`;
                } catch (error) {
                    console.error('PDF extraction error:', error);
                    return `PDF file: ${file.name}`;
                }
            };

            // Extract text from Excel
            const extractExcelText = async (file) => {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                    let text = '';
                    
                    workbook.SheetNames.forEach(sheetName => {
                        const sheet = workbook.Sheets[sheetName];
                        text += `Sheet: ${sheetName}\n`;
                        text += XLSX.utils.sheet_to_csv(sheet) + '\n\n';
                    });
                    
                    return text;
                } catch (error) {
                    console.error('Excel extraction error:', error);
                    return `Excel file: ${file.name}`;
                }
            };

            // Extract text from image (placeholder for OCR)
            const extractImageText = async (file) => {
                // In a real implementation, you'd use OCR or send image directly to Gemini
                return `Image file uploaded: ${file.name}. This image will be processed by Gemini API for text extraction.`;
            };

            // Get field definitions for each page type
            const getFieldDefinitions = (pageId) => {
                const definitions = {
                    trajectory: {
                        description: "Well Trajectory Document - Extract well trajectory information including coordinates, elevations, and departure data",
                        fields: ["sequenceNo", "field", "well", "serviceLocation", "reservoirLocation", "azimuth", "utmZone", "easting", "northing", "latitude", "longitude", "rigNumber", "groundLevel", "rtkb", "departure data (feet, meters, eastingLP, northingLP, td)"]
                    },
                    geological: {
                        description: "Geological Prediction Document - Extract formation data, nearby wells information, and geological planning details",
                        fields: ["wellName", "productionType", "formation tops with depths", "nearby wells data", "plan information (tvdss, tvdrtkb, dap, remarks)"]
                    },
                    location: {
                        description: "Location Memorandum - Extract well location details, coordinates, and administrative information",
                        fields: ["subject", "subLocation", "well", "objective", "sequence", "number", "from", "date", "field", "wellNumber", "rig", "coordinates (easting, northing, latitude, longitude)", "elevations", "utm zone", "datum"]
                    },
                    prognosis: {
                        description: "Well Prognosis Document - Extract prognosis information, rates, duration, and operational details",
                        fields: ["wellName", "rigName", "wellType", "prognosisStatus", "jobSequence", "field", "orientation", "targetedZones", "jobDescription", "budgetCategory", "durations", "expectedDate", "production rates", "control points", "operational details"]
                    },
                    completion: {
                        description: "Completion Allocation Document - Extract completion design, casing information, and equipment details",
                        fields: ["date", "completionType", "completionCode", "stopDate", "rig", "well", "casingSize", "casingWeight", "tieBack", "wrapOver", "tubingThread", "lower completion items", "upper completion items"]
                    },
                    tubular: {
                        description: "Tubular Allocation Document - Extract tubular specifications, sizes, and supplier information",
                        fields: ["wellNumber", "field", "rig", "wellType", "tubular specifications including size, segment, type, item, weight, grade, connection, supplier, SAP, store, contact"]
                    },
                    wellInfo: {
                        description: "Well Information Document - Extract equipment allocation requirements and planning details",
                        fields: ["rig", "spotDate", "well", "wellType", "expectedSpotDate", "productionTest", "formationFluidType", "casingDesign", "contingencyPlan", "specialRequirements", "SAP information"]
                    },
                    wellhead: {
                        description: "Wellhead Allocation Document - Extract wellhead equipment, installation details, and specifications",
                        fields: ["wellNumber", "serviceLocation", "rate", "lastWorkOrder", "whInstallationDate", "wellObjective", "whCode", "expectedSpotDate", "equipment details including item, description, SAP number, quantity, store, remarks"]
                    }
                };
                return definitions[pageId] || { description: "General well document", fields: [] };
            };

            // Call Gemini API for document processing
            const callGeminiAPI = async (documentContent, fieldDefinitions, pageId) => {
                try {
                    const prompt = `You are an AI assistant specialized in extracting information from oil and gas well documents.

Document Type: ${fieldDefinitions.description}

Expected Fields to Extract: ${fieldDefinitions.fields.join(', ')}

Document Content:
${documentContent}

Please extract the relevant information from this document and return it as a JSON object that matches the expected fields. For table data (like formations, equipment, etc.), return arrays of objects. If a field is not found in the document, return an empty string or empty array as appropriate.

Return only valid JSON without any explanation or additional text.`;

                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiApiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }]
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`API request failed: ${response.status}`);
                    }

                    const result = await response.json();
                    const extractedText = result.candidates[0].content.parts[0].text;
                    
                    // Try to parse the JSON response
                    try {
                        const cleanedText = extractedText.replace(/```json\n?|\n?```/g, '').trim();
                        return JSON.parse(cleanedText);
                    } catch (parseError) {
                        console.error('Failed to parse API response as JSON:', parseError);
                        throw new Error('Invalid JSON response from API');
                    }
                } catch (error) {
                    console.error('Gemini API call failed:', error);
                    throw error;
                }
            };

            // Get empty row based on table type
            const getEmptyRow = (tableName) => {
                const templates = {
                    elevations: { category: '', gl: '', rtkb: '' },
                    formationTops: { formation: '', depth0: '', depth250: '', depth317: '', depth389: '' },
                    nearbyWells: { uwi: '', layerName: '', topMD: '', baseMD: '', topTVDS: '', botTVDS: '', rtkb: '', thickness: '' },
                    controlPoints: { type: '', description: '', coordE: '', coordN: '', zone: '', remark: '' },
                    cuttings: { fromFormation: '', toFormation: '', frequency: '', purpose: '', remark: '' },
                    coring: { coreNumber: '', intervalFrom: '', intervalTo: '', length: '', preserved: 'No', holeType: '', mudType: '' },
                    logging: { runNumber: '', holeType: '', service: '', techniques: '', remarks: '' },
                    testing: { formation: '', intervalFrom: '', intervalTo: '', testType: '', objective: '' },
                    lowerCompletion: { itemNumber: '', dce: '', size: '', description: '', weight: '', make: '', material: '', mcs: '', store: '', quantity: '' },
                    upperCompletion: { dce: '', vmi: '', size: '', description: '', weight: '', make: '', material: '', msc: '', store: '', quantity: '', remark: '' },
                    tubulars: { size: '', segment: '', type: '', item: '', weight: '', grade: '', connection: '', supplier: '', sap: '', store: '', contact: '' },
                    equipment: { item: '', description: '', sapNumber: '', qty: '', store: '', remark: '' }
                };
                return templates[tableName] || {};
            };

            // Save current page
            const savePage = () => {
                const pageId = pages[currentPage].id;
                setPageStatus(prev => ({ ...prev, [pageId]: true }));
            };

            // Calculate progress
            const calculateProgress = () => {
                const completed = Object.values(pageStatus).filter(status => status).length;
                return (completed / pages.length) * 100;
            };

            // Generate final Excel file
            const generateFinalProgram = () => {
                const wb = XLSX.utils.book_new();
                
                // Create One Page Program summary
                const summaryData = [
                    ['ONE PAGE WELL PROGRAM'],
                    ['Generated: ' + new Date().toLocaleDateString()],
                    [''],
                    ['BASIC INFORMATION'],
                    ['Well Number', formData.location.wellNumber || formData.trajectory.well],
                    ['Field', formData.location.field || formData.trajectory.field],
                    ['Rig', formData.location.rig || formData.prognosis.rigName],
                    ['Location', formData.trajectory.serviceLocation],
                    [''],
                    ['WELL OBJECTIVE'],
                    [formData.location.objective || formData.wellhead.wellObjective],
                    [''],
                    ['COORDINATES'],
                    ['Easting', formData.location.easting || formData.trajectory.easting],
                    ['Northing', formData.location.northing || formData.trajectory.northing],
                    ['Latitude', formData.location.latitude || formData.trajectory.latitude],
                    ['Longitude', formData.location.longitude || formData.trajectory.longitude],
                    ['Ground Level', formData.location.elevation.gl || formData.trajectory.groundLevel],
                    ['RTKB', formData.location.elevation.rtkb || formData.trajectory.rtkb],
                    [''],
                    ['TRAJECTORY'],
                    ['Azimuth', formData.trajectory.azimuth],
                    ['Total Depth', formData.trajectory.departure.td],
                    ['Reservoir', formData.trajectory.reservoirLocation],
                    [''],
                    ['PRODUCTION RATES (BOPD)'],
                    ['Type', 'SS', 'LS', 'ST'],
                    ['DWS Rate', formData.prognosis.dwsRate.ss, formData.prognosis.dwsRate.ls, formData.prognosis.dwsRate.st],
                    ['Prognosis', formData.prognosis.prognosisRate.ss, formData.prognosis.prognosisRate.ls, formData.prognosis.prognosisRate.st],
                    [''],
                    ['DURATION'],
                    ['Basic Duration', formData.prognosis.basicDuration + ' days'],
                    ['Total Duration', formData.prognosis.totalDuration + ' days'],
                    ['Expected Date', formData.prognosis.expectedDate]
                ];
                
                const ws = XLSX.utils.aoa_to_sheet(summaryData);
                XLSX.utils.book_append_sheet(wb, ws, 'One Page Program');
                
                // Add individual data sheets
                pages.forEach(page => {
                    const pageData = formData[page.id];
                    const sheetData = [];
                    
                    // Convert page data to array format
                    Object.entries(pageData).forEach(([key, value]) => {
                        if (Array.isArray(value)) {
                            sheetData.push([key.toUpperCase()]);
                            if (value.length > 0) {
                                sheetData.push(Object.keys(value[0]));
                                value.forEach(row => {
                                    sheetData.push(Object.values(row));
                                });
                            }
                            sheetData.push([]);
                        } else if (typeof value === 'object' && value !== null) {
                            sheetData.push([key.toUpperCase()]);
                            Object.entries(value).forEach(([k, v]) => {
                                sheetData.push([k, v]);
                            });
                            sheetData.push([]);
                        } else {
                            sheetData.push([key, value]);
                        }
                    });
                    
                    const pageWs = XLSX.utils.aoa_to_sheet(sheetData);
                    XLSX.utils.book_append_sheet(wb, pageWs, page.title);
                });
                
                // Generate and download Excel file
                XLSX.writeFile(wb, 'Well_Documentation_Complete.xlsx');
            };

            // Render current page form
            const renderPageContent = () => {
                const pageId = pages[currentPage].id;
                const pageData = formData[pageId];
                const fileUploadProps = {
                    pageId,
                    onFileUpload: handleFileUpload,
                    uploadedFile: uploadedFiles[pageId],
                    processingStatus: processingStatus[pageId],
                    extractionResult: extractionResults[pageId],
                    geminiApiKey
                };
                
                switch(pageId) {
                    case 'trajectory':
                        return <TrajectoryForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    case 'geological':
                        return <GeologicalForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    case 'location':
                        return <LocationForm data={pageData} updateData={updateFormData} {...fileUploadProps} />;
                    case 'prognosis':
                        return <PrognosisForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    case 'completion':
                        return <CompletionForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    case 'tubular':
                        return <TubularForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    case 'wellInfo':
                        return <WellInfoForm data={pageData} updateData={updateFormData} {...fileUploadProps} />;
                    case 'wellhead':
                        return <WellheadForm data={pageData} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} updateRow={updateTableRow} {...fileUploadProps} />;
                    default:
                        return <div className="empty-state"><h3>Select a section to begin</h3></div>;
                }
            };

            return (
                <div className="app-container">
                    <div className="sidebar">
                        <h2>📋 Documentation Sections</h2>
                        
                        {/* API Configuration */}
                        <div className="api-config">
                            <h4>🔑 Gemini API Configuration</h4>
                            <input 
                                type="password" 
                                placeholder="Enter Gemini API Key" 
                                className="form-input"
                                value={geminiApiKey}
                                onChange={(e) => setGeminiApiKey(e.target.value)}
                                style={{ fontSize: '12px', marginBottom: '5px' }}
                            />
                            <div style={{ fontSize: '11px', color: '#718096' }}>
                                Required for automatic document extraction
                            </div>
                        </div>

                        <div className="progress-indicator">
                            <div className="progress-bar">
                                <div className="progress-fill" style={{ width: `${calculateProgress()}%` }}></div>
                            </div>
                            <span className="progress-text">{Math.round(calculateProgress())}%</span>
                        </div>
                        
                        {pages.map((page, index) => (
                            <div
                                key={page.id}
                                className={`nav-item ${currentPage === index ? 'active' : ''} ${pageStatus[page.id] ? 'completed' : ''}`}
                                onClick={() => setCurrentPage(index)}
                            >
                                <div className="nav-number">{pageStatus[page.id] ? '✓' : index + 1}</div>
                                <div className="nav-text">{page.title}</div>
                                <div className="nav-badge">{page.type}</div>
                            </div>
                        ))}
                        
                        <div className="action-section">
                            <h3 style={{ fontSize: '16px', marginBottom: '15px', color: '#1a365d' }}>Final Actions</h3>
                            <div className="final-actions">
                                <button 
                                    className="btn btn-success" 
                                    style={{ width: '100%' }}
                                    onClick={generateFinalProgram}
                                >
                                    📊 Generate Excel Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="page-header">
                            <h1>{pages[currentPage].title}</h1>
                            <p>
                                Document Type: {pages[currentPage].type} | 
                                <span className={`status-indicator ${pageStatus[pages[currentPage].id] ? 'status-complete' : 'status-incomplete'}`}>
                                    {pageStatus[pages[currentPage].id] ? '✓ Complete' : '○ Incomplete'}
                                </span>
                            </p>
                        </div>

                        {calculateProgress() === 100 && (
                            <div className="alert alert-success">
                                ✓ All sections completed! You can now generate the final Excel report.
                            </div>
                        )}

                        {renderPageContent()}

                        <div className="btn-group">
                            <button 
                                className="btn btn-secondary" 
                                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                                disabled={currentPage === 0}
                            >
                                ← Previous
                            </button>
                            <button 
                                className="btn btn-primary" 
                                onClick={savePage}
                            >
                                ✓ Save This Section
                            </button>
                            <button 
                                className="btn btn-primary" 
                                onClick={() => setCurrentPage(Math.min(pages.length - 1, currentPage + 1))}
                                disabled={currentPage === pages.length - 1}
                            >
                                Next →
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        // File Upload Component
        function FileUploadSection({ pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            const fileInputRef = React.useRef(null);
            
            const handleFileSelect = (event) => {
                const file = event.target.files[0];
                if (file) {
                    onFileUpload(pageId, file);
                }
            };

            const getStatusMessage = () => {
                switch (processingStatus) {
                    case 'processing':
                        return (
                            <div className="extraction-status extraction-processing">
                                <div className="processing-spinner"></div>
                                Processing document with Gemini AI...
                            </div>
                        );
                    case 'success':
                        return (
                            <div className="extraction-status extraction-success">
                                ✓ Document processed successfully! Form has been auto-filled.
                            </div>
                        );
                    case 'error':
                        return (
                            <div className="extraction-status extraction-error">
                                ✗ Processing failed. Please check your API key and try again.
                            </div>
                        );
                    default:
                        return null;
                }
            };

            return (
                <div className={`upload-section ${processingStatus || ''}`}>
                    <h4 style={{ marginBottom: '15px', color: '#1a365d' }}>📄 Upload Document for Auto-Fill</h4>
                    
                    <input 
                        ref={fileInputRef}
                        type="file" 
                        accept=".pdf,.xlsx,.xls,.doc,.docx,.jpg,.jpeg,.png,.bmp,.tiff"
                        onChange={handleFileSelect}
                        className="upload-input"
                    />
                    
                    <button 
                        className="upload-button"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={processingStatus === 'processing'}
                    >
                        {processingStatus === 'processing' ? 'Processing...' : '📁 Choose Document'}
                    </button>
                    
                    <div className="upload-text">
                        Supports: PDF, Excel, Word, Images (JPG, PNG, etc.)
                    </div>
                    
                    {!geminiApiKey && (
                        <div style={{ color: '#e53e3e', fontSize: '12px', marginTop: '5px' }}>
                            ⚠️ Please enter Gemini API key in sidebar to enable auto-extraction
                        </div>
                    )}
                    
                    {uploadedFile && (
                        <div className="file-info">
                            <div>
                                <div className="file-name">{uploadedFile.name}</div>
                                <div className="file-size">{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</div>
                            </div>
                        </div>
                    )}
                    
                    {getStatusMessage()}
                    
                    {extractionResult && (
                        <div style={{ fontSize: '12px', color: '#718096', marginTop: '10px' }}>
                            Found {Object.keys(extractionResult).length} fields in document
                        </div>
                    )}
                </div>
            );
        }

        // Individual Form Components
        function TrajectoryForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>General Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Sequence Number</label>
                                <input className="form-input" value={data.sequenceNo} onChange={(e) => updateData('trajectory', { sequenceNo: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input className="form-input" value={data.field} onChange={(e) => updateData('trajectory', { field: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input className="form-input" value={data.well} onChange={(e) => updateData('trajectory', { well: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Service Location</label>
                                <input className="form-input" value={data.serviceLocation} onChange={(e) => updateData('trajectory', { serviceLocation: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Reservoir Location</label>
                                <input className="form-input" value={data.reservoirLocation} onChange={(e) => updateData('trajectory', { reservoirLocation: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Azimuth (degrees)</label>
                                <input className="form-input" value={data.azimuth} onChange={(e) => updateData('trajectory', { azimuth: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Coordinates</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>UTM Zone</label>
                                <input className="form-input" value={data.utmZone} onChange={(e) => updateData('trajectory', { utmZone: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Easting</label>
                                <input className="form-input" value={data.easting} onChange={(e) => updateData('trajectory', { easting: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Northing</label>
                                <input className="form-input" value={data.northing} onChange={(e) => updateData('trajectory', { northing: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Latitude</label>
                                <input className="form-input" value={data.latitude} onChange={(e) => updateData('trajectory', { latitude: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Longitude</label>
                                <input className="form-input" value={data.longitude} onChange={(e) => updateData('trajectory', { longitude: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Elevations</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Rig Number</label>
                                <input className="form-input" value={data.rigNumber} onChange={(e) => updateData('trajectory', { rigNumber: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Ground Level (m)</label>
                                <input className="form-input" value={data.groundLevel} onChange={(e) => updateData('trajectory', { groundLevel: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>RTKB (m)</label>
                                <input className="form-input" value={data.rtkb} onChange={(e) => updateData('trajectory', { rtkb: e.target.value })} />
                            </div>
                        </div>
                        <button className="btn btn-add" onClick={() => addRow('trajectory', 'elevations')}>Add Elevation Data</button>
                        {data.elevations && data.elevations.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Category</th>
                                            <th>Ground Level</th>
                                            <th>RTKB</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.elevations.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.category} onChange={(e) => updateRow('trajectory', 'elevations', index, 'category', e.target.value)} /></td>
                                                <td><input value={row.gl} onChange={(e) => updateRow('trajectory', 'elevations', index, 'gl', e.target.value)} /></td>
                                                <td><input value={row.rtkb} onChange={(e) => updateRow('trajectory', 'elevations', index, 'rtkb', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('trajectory', 'elevations', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>

                    <div className="form-section">
                        <h3>Departure</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Departure (feet)</label>
                                <input className="form-input" value={data.departure.feet} onChange={(e) => updateData('trajectory', { departure: { ...data.departure, feet: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>Departure (meters)</label>
                                <input className="form-input" value={data.departure.meters} onChange={(e) => updateData('trajectory', { departure: { ...data.departure, meters: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>LP Easting</label>
                                <input className="form-input" value={data.departure.eastingLP} onChange={(e) => updateData('trajectory', { departure: { ...data.departure, eastingLP: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>LP Northing</label>
                                <input className="form-input" value={data.departure.northingLP} onChange={(e) => updateData('trajectory', { departure: { ...data.departure, northingLP: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>Total Depth (TD)</label>
                                <input className="form-input" value={data.departure.td} onChange={(e) => updateData('trajectory', { departure: { ...data.departure, td: e.target.value } })} />
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        function GeologicalForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Basic Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Name</label>
                                <input className="form-input" value={data.wellName} onChange={(e) => updateData('geological', { wellName: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Production Type</label>
                                <input className="form-input" value={data.productionType} onChange={(e) => updateData('geological', { productionType: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Formation Tops</h3>
                        <button className="btn btn-add" onClick={() => addRow('geological', 'formationTops')}>Add Formation</button>
                        {data.formationTops && data.formationTops.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Formation</th>
                                            <th>0</th>
                                            <th>250</th>
                                            <th>317</th>
                                            <th>389</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.formationTops.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.formation} onChange={(e) => updateRow('geological', 'formationTops', index, 'formation', e.target.value)} /></td>
                                                <td><input value={row.depth0} onChange={(e) => updateRow('geological', 'formationTops', index, 'depth0', e.target.value)} /></td>
                                                <td><input value={row.depth250} onChange={(e) => updateRow('geological', 'formationTops', index, 'depth250', e.target.value)} /></td>
                                                <td><input value={row.depth317} onChange={(e) => updateRow('geological', 'formationTops', index, 'depth317', e.target.value)} /></td>
                                                <td><input value={row.depth389} onChange={(e) => updateRow('geological', 'formationTops', index, 'depth389', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('geological', 'formationTops', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>

                    <div className="form-section">
                        <h3>Plan</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>TVDSS</label>
                                <input className="form-input" value={data.plan.tvdss} onChange={(e) => updateData('geological', { plan: { ...data.plan, tvdss: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>TVDRTKB</label>
                                <input className="form-input" value={data.plan.tvdrtkb} onChange={(e) => updateData('geological', { plan: { ...data.plan, tvdrtkb: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>DAP</label>
                                <input className="form-input" value={data.plan.dap} onChange={(e) => updateData('geological', { plan: { ...data.plan, dap: e.target.value } })} />
                            </div>
                        </div>
                        <div className="form-group">
                            <label>Remarks</label>
                            <textarea className="form-textarea" value={data.plan.remarks} onChange={(e) => updateData('geological', { plan: { ...data.plan, remarks: e.target.value } })} />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Nearby Wells</h3>
                        <button className="btn btn-add" onClick={() => addRow('geological', 'nearbyWells')}>Add Nearby Well</button>
                        {data.nearbyWells && data.nearbyWells.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>UWI</th>
                                            <th>Layer Name</th>
                                            <th>Top MD</th>
                                            <th>Base MD</th>
                                            <th>Top TVDS</th>
                                            <th>BOT TVDS</th>
                                            <th>RTKB</th>
                                            <th>Thickness</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.nearbyWells.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.uwi} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'uwi', e.target.value)} /></td>
                                                <td><input value={row.layerName} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'layerName', e.target.value)} /></td>
                                                <td><input value={row.topMD} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'topMD', e.target.value)} /></td>
                                                <td><input value={row.baseMD} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'baseMD', e.target.value)} /></td>
                                                <td><input value={row.topTVDS} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'topTVDS', e.target.value)} /></td>
                                                <td><input value={row.botTVDS} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'botTVDS', e.target.value)} /></td>
                                                <td><input value={row.rtkb} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'rtkb', e.target.value)} /></td>
                                                <td><input value={row.thickness} onChange={(e) => updateRow('geological', 'nearbyWells', index, 'thickness', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('geological', 'nearbyWells', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        function LocationForm({ data, updateData, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Memorandum Header</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Subject</label>
                                <input className="form-input" value={data.subject} onChange={(e) => updateData('location', { subject: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Sub-Location</label>
                                <input className="form-input" value={data.subLocation} onChange={(e) => updateData('location', { subLocation: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input className="form-input" value={data.well} onChange={(e) => updateData('location', { well: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Sequence</label>
                                <input className="form-input" value={data.sequence} onChange={(e) => updateData('location', { sequence: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Number</label>
                                <input className="form-input" value={data.number} onChange={(e) => updateData('location', { number: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>From</label>
                                <input className="form-input" value={data.from} onChange={(e) => updateData('location', { from: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Date</label>
                                <input className="form-input" type="date" value={data.date} onChange={(e) => updateData('location', { date: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input className="form-input" value={data.field} onChange={(e) => updateData('location', { field: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well Number</label>
                                <input className="form-input" value={data.wellNumber} onChange={(e) => updateData('location', { wellNumber: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input className="form-input" value={data.rig} onChange={(e) => updateData('location', { rig: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Well Objective</h3>
                        <div className="form-group">
                            <label>Objective</label>
                            <textarea className="form-textarea" value={data.objective} onChange={(e) => updateData('location', { objective: e.target.value })} placeholder="Enter well objective..." />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Coordinates</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Easting</label>
                                <input className="form-input" value={data.easting} onChange={(e) => updateData('location', { easting: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Northing</label>
                                <input className="form-input" value={data.northing} onChange={(e) => updateData('location', { northing: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Ground Level (m)</label>
                                <input className="form-input" value={data.elevation.gl} onChange={(e) => updateData('location', { elevation: { ...data.elevation, gl: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>RTKB (m)</label>
                                <input className="form-input" value={data.elevation.rtkb} onChange={(e) => updateData('location', { elevation: { ...data.elevation, rtkb: e.target.value } })} />
                            </div>
                            <div className="form-group">
                                <label>Latitude</label>
                                <input className="form-input" value={data.latitude} onChange={(e) => updateData('location', { latitude: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Longitude</label>
                                <input className="form-input" value={data.longitude} onChange={(e) => updateData('location', { longitude: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>UTM Zone</label>
                                <input className="form-input" value={data.utmZone} onChange={(e) => updateData('location', { utmZone: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Datum</label>
                                <input className="form-input" value={data.datum} onChange={(e) => updateData('location', { datum: e.target.value })} />
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        function PrognosisForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Basic Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Name</label>
                                <input className="form-input" value={data.wellName} onChange={(e) => updateData('prognosis', { wellName: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Rig Name</label>
                                <input className="form-input" value={data.rigName} onChange={(e) => updateData('prognosis', { rigName: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <select className="form-select" value={data.wellType} onChange={(e) => updateData('prognosis', { wellType: e.target.value })}>
                                    <option value="">Select Type</option>
                                    <option value="Development Producer">Development Producer</option>
                                    <option value="Water Injector">Water Injector</option>
                                    <option value="Gas Injector">Gas Injector</option>
                                    <option value="Exploration">Exploration</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Prognosis Status</label>
                                <select className="form-select" value={data.prognosisStatus} onChange={(e) => updateData('prognosis', { prognosisStatus: e.target.value })}>
                                    <option value="">Select Status</option>
                                    <option value="Draft">Draft</option>
                                    <option value="Final">Final</option>
                                    <option value="Approved">Approved</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Job Sequence</label>
                                <input className="form-input" value={data.jobSequence} onChange={(e) => updateData('prognosis', { jobSequence: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Summary Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Field</label>
                                <input className="form-input" value={data.field} onChange={(e) => updateData('prognosis', { field: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Orientation</label>
                                <input className="form-input" value={data.orientation} onChange={(e) => updateData('prognosis', { orientation: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Targeted Zones</label>
                                <input className="form-input" value={data.targetedZones} onChange={(e) => updateData('prognosis', { targetedZones: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Job Description</label>
                                <input className="form-input" value={data.jobDescription} onChange={(e) => updateData('prognosis', { jobDescription: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Budget Category</label>
                                <input className="form-input" value={data.budgetCategory} onChange={(e) => updateData('prognosis', { budgetCategory: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Basic Duration (days)</label>
                                <input className="form-input" type="number" value={data.basicDuration} onChange={(e) => updateData('prognosis', { basicDuration: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Total Duration (days)</label>
                                <input className="form-input" type="number" value={data.totalDuration} onChange={(e) => updateData('prognosis', { totalDuration: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Expected Date</label>
                                <input className="form-input" type="date" value={data.expectedDate} onChange={(e) => updateData('prognosis', { expectedDate: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Potential Rates</h3>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Rate Type</th>
                                        <th>SS</th>
                                        <th>LS</th>
                                        <th>ST</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>DWS Rate (BOPD)</td>
                                        <td><input value={data.dwsRate.ss} onChange={(e) => updateData('prognosis', { dwsRate: { ...data.dwsRate, ss: e.target.value } })} /></td>
                                        <td><input value={data.dwsRate.ls} onChange={(e) => updateData('prognosis', { dwsRate: { ...data.dwsRate, ls: e.target.value } })} /></td>
                                        <td><input value={data.dwsRate.st} onChange={(e) => updateData('prognosis', { dwsRate: { ...data.dwsRate, st: e.target.value } })} /></td>
                                    </tr>
                                    <tr>
                                        <td>Prognosis Rate (BOPD)</td>
                                        <td><input value={data.prognosisRate.ss} onChange={(e) => updateData('prognosis', { prognosisRate: { ...data.prognosisRate, ss: e.target.value } })} /></td>
                                        <td><input value={data.prognosisRate.ls} onChange={(e) => updateData('prognosis', { prognosisRate: { ...data.prognosisRate, ls: e.target.value } })} /></td>
                                        <td><input value={data.prognosisRate.st} onChange={(e) => updateData('prognosis', { prognosisRate: { ...data.prognosisRate, st: e.target.value } })} /></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Control Points</h3>
                        <button className="btn btn-add" onClick={() => addRow('prognosis', 'controlPoints')}>Add Control Point</button>
                        {data.controlPoints && data.controlPoints.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Coord E</th>
                                            <th>Coord N</th>
                                            <th>Zone</th>
                                            <th>Remark</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.controlPoints.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.type} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'type', e.target.value)} /></td>
                                                <td><input value={row.description} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'description', e.target.value)} /></td>
                                                <td><input value={row.coordE} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'coordE', e.target.value)} /></td>
                                                <td><input value={row.coordN} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'coordN', e.target.value)} /></td>
                                                <td><input value={row.zone} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'zone', e.target.value)} /></td>
                                                <td><input value={row.remark} onChange={(e) => updateRow('prognosis', 'controlPoints', index, 'remark', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('prognosis', 'controlPoints', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        function CompletionForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Allocation Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Date</label>
                                <input className="form-input" type="date" value={data.date} onChange={(e) => updateData('completion', { date: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Completion Type</label>
                                <input className="form-input" value={data.completionType} onChange={(e) => updateData('completion', { completionType: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Completion Code</label>
                                <input className="form-input" value={data.completionCode} onChange={(e) => updateData('completion', { completionCode: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Stop Date</label>
                                <input className="form-input" type="date" value={data.stopDate} onChange={(e) => updateData('completion', { stopDate: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input className="form-input" value={data.rig} onChange={(e) => updateData('completion', { rig: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input className="form-input" value={data.well} onChange={(e) => updateData('completion', { well: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Casing/Liner Size</label>
                                <input className="form-input" value={data.casingSize} onChange={(e) => updateData('completion', { casingSize: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Casing/Liner Weight</label>
                                <input className="form-input" value={data.casingWeight} onChange={(e) => updateData('completion', { casingWeight: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Tie Back</label>
                                <select className="form-select" value={data.tieBack} onChange={(e) => updateData('completion', { tieBack: e.target.value })}>
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Wrap Over</label>
                                <select className="form-select" value={data.wrapOver} onChange={(e) => updateData('completion', { wrapOver: e.target.value })}>
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Tubing Thread</label>
                                <input className="form-input" value={data.tubingThread} onChange={(e) => updateData('completion', { tubingThread: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Lower Completion</h3>
                        <button className="btn btn-add" onClick={() => addRow('completion', 'lowerCompletion')}>Add Item</button>
                        {data.lowerCompletion && data.lowerCompletion.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Item#</th>
                                            <th>DCE</th>
                                            <th>Size</th>
                                            <th>Description</th>
                                            <th>Weight</th>
                                            <th>Make</th>
                                            <th>Material</th>
                                            <th>MCS</th>
                                            <th>Store</th>
                                            <th>Qty</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.lowerCompletion.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.itemNumber} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'itemNumber', e.target.value)} /></td>
                                                <td><input value={row.dce} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'dce', e.target.value)} /></td>
                                                <td><input value={row.size} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'size', e.target.value)} /></td>
                                                <td><input value={row.description} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'description', e.target.value)} /></td>
                                                <td><input value={row.weight} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'weight', e.target.value)} /></td>
                                                <td><input value={row.make} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'make', e.target.value)} /></td>
                                                <td><input value={row.material} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'material', e.target.value)} /></td>
                                                <td><input value={row.mcs} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'mcs', e.target.value)} /></td>
                                                <td><input value={row.store} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'store', e.target.value)} /></td>
                                                <td><input value={row.quantity} onChange={(e) => updateRow('completion', 'lowerCompletion', index, 'quantity', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('completion', 'lowerCompletion', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        function TubularForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Well Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Number</label>
                                <input className="form-input" value={data.wellNumber} onChange={(e) => updateData('tubular', { wellNumber: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input className="form-input" value={data.field} onChange={(e) => updateData('tubular', { field: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input className="form-input" value={data.rig} onChange={(e) => updateData('tubular', { rig: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <input className="form-input" value={data.wellType} onChange={(e) => updateData('tubular', { wellType: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Tubular Specifications</h3>
                        <button className="btn btn-add" onClick={() => addRow('tubular', 'tubulars')}>Add Tubular</button>
                        {data.tubulars && data.tubulars.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Size</th>
                                            <th>Segment</th>
                                            <th>Type</th>
                                            <th>Item</th>
                                            <th>Weight</th>
                                            <th>Grade</th>
                                            <th>Connection</th>
                                            <th>Supplier</th>
                                            <th>SAP</th>
                                            <th>Store</th>
                                            <th>Contact</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.tubulars.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.size} onChange={(e) => updateRow('tubular', 'tubulars', index, 'size', e.target.value)} /></td>
                                                <td><input value={row.segment} onChange={(e) => updateRow('tubular', 'tubulars', index, 'segment', e.target.value)} /></td>
                                                <td><input value={row.type} onChange={(e) => updateRow('tubular', 'tubulars', index, 'type', e.target.value)} /></td>
                                                <td><input value={row.item} onChange={(e) => updateRow('tubular', 'tubulars', index, 'item', e.target.value)} /></td>
                                                <td><input value={row.weight} onChange={(e) => updateRow('tubular', 'tubulars', index, 'weight', e.target.value)} /></td>
                                                <td><input value={row.grade} onChange={(e) => updateRow('tubular', 'tubulars', index, 'grade', e.target.value)} /></td>
                                                <td><input value={row.connection} onChange={(e) => updateRow('tubular', 'tubulars', index, 'connection', e.target.value)} /></td>
                                                <td><input value={row.supplier} onChange={(e) => updateRow('tubular', 'tubulars', index, 'supplier', e.target.value)} /></td>
                                                <td><input value={row.sap} onChange={(e) => updateRow('tubular', 'tubulars', index, 'sap', e.target.value)} /></td>
                                                <td><input value={row.store} onChange={(e) => updateRow('tubular', 'tubulars', index, 'store', e.target.value)} /></td>
                                                <td><input value={row.contact} onChange={(e) => updateRow('tubular', 'tubulars', index, 'contact', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('tubular', 'tubulars', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        function WellInfoForm({ data, updateData, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Information Required for Equipment Allocation</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Rig</label>
                                <input className="form-input" value={data.rig} onChange={(e) => updateData('wellInfo', { rig: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Spot Date (Latest PP)</label>
                                <input className="form-input" type="date" value={data.spotDate} onChange={(e) => updateData('wellInfo', { spotDate: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input className="form-input" value={data.well} onChange={(e) => updateData('wellInfo', { well: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <input className="form-input" value={data.wellType} onChange={(e) => updateData('wellInfo', { wellType: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Expected Spot Date</label>
                                <input className="form-input" type="date" value={data.expectedSpotDate} onChange={(e) => updateData('wellInfo', { expectedSpotDate: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Production Test</label>
                                <select className="form-select" value={data.productionTest} onChange={(e) => updateData('wellInfo', { productionTest: e.target.value })}>
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Formation Fluid Type</label>
                                <input className="form-input" value={data.formationFluidType} onChange={(e) => updateData('wellInfo', { formationFluidType: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Casing Design</label>
                                <select className="form-select" value={data.casingDesign} onChange={(e) => updateData('wellInfo', { casingDesign: e.target.value })}>
                                    <option value="">Select</option>
                                    <option value="LCD">LCD</option>
                                    <option value="HCD">HCD</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>SAP Reported</label>
                                <select className="form-select" value={data.sapReported} onChange={(e) => updateData('wellInfo', { sapReported: e.target.value })}>
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>SAP Committee Well</label>
                                <select className="form-select" value={data.sapCommitteeWell} onChange={(e) => updateData('wellInfo', { sapCommitteeWell: e.target.value })}>
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Planning Details</h3>
                        <div className="form-group">
                            <label>Contingency Plan</label>
                            <textarea className="form-textarea" value={data.contingencyPlan} onChange={(e) => updateData('wellInfo', { contingencyPlan: e.target.value })} placeholder="Enter contingency plan details..." />
                        </div>
                        <div className="form-group">
                            <label>Special Requirements</label>
                            <textarea className="form-textarea" value={data.specialRequirements} onChange={(e) => updateData('wellInfo', { specialRequirements: e.target.value })} placeholder="Enter any special requirements..." />
                        </div>
                    </div>
                </div>
            );
        }

        function WellheadForm({ data, updateData, addRow, removeRow, updateRow, pageId, onFileUpload, uploadedFile, processingStatus, extractionResult, geminiApiKey }) {
            return (
                <div>
                    <FileUploadSection 
                        pageId={pageId}
                        onFileUpload={onFileUpload}
                        uploadedFile={uploadedFile}
                        processingStatus={processingStatus}
                        extractionResult={extractionResult}
                        geminiApiKey={geminiApiKey}
                    />
                    
                    <div className="form-card">
                    <div className="form-section">
                        <h3>Wellhead Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Number</label>
                                <input className="form-input" value={data.wellNumber} onChange={(e) => updateData('wellhead', { wellNumber: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Service Location</label>
                                <input className="form-input" value={data.serviceLocation} onChange={(e) => updateData('wellhead', { serviceLocation: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Rate</label>
                                <input className="form-input" value={data.rate} onChange={(e) => updateData('wellhead', { rate: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Last Work Order (WO)</label>
                                <input className="form-input" value={data.lastWorkOrder} onChange={(e) => updateData('wellhead', { lastWorkOrder: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>WH Installation Date</label>
                                <input className="form-input" type="date" value={data.whInstallationDate} onChange={(e) => updateData('wellhead', { whInstallationDate: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>WH Code</label>
                                <input className="form-input" value={data.whCode} onChange={(e) => updateData('wellhead', { whCode: e.target.value })} />
                            </div>
                            <div className="form-group">
                                <label>Expected Spot Date</label>
                                <input className="form-input" type="date" value={data.expectedSpotDate} onChange={(e) => updateData('wellhead', { expectedSpotDate: e.target.value })} />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Well Objective</h3>
                        <div className="form-group">
                            <label>Objective</label>
                            <textarea className="form-textarea" value={data.wellObjective} onChange={(e) => updateData('wellhead', { wellObjective: e.target.value })} placeholder="Enter well objective..." />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Wellhead Equipment</h3>
                        <button className="btn btn-add" onClick={() => addRow('wellhead', 'equipment')}>Add Equipment</button>
                        {data.equipment && data.equipment.length > 0 && (
                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Description</th>
                                            <th>SAP Number</th>
                                            <th>QTY</th>
                                            <th>Store</th>
                                            <th>Remark</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.equipment.map((row, index) => (
                                            <tr key={index}>
                                                <td><input value={row.item} onChange={(e) => updateRow('wellhead', 'equipment', index, 'item', e.target.value)} /></td>
                                                <td><input value={row.description} onChange={(e) => updateRow('wellhead', 'equipment', index, 'description', e.target.value)} /></td>
                                                <td><input value={row.sapNumber} onChange={(e) => updateRow('wellhead', 'equipment', index, 'sapNumber', e.target.value)} /></td>
                                                <td><input value={row.qty} onChange={(e) => updateRow('wellhead', 'equipment', index, 'qty', e.target.value)} /></td>
                                                <td><input value={row.store} onChange={(e) => updateRow('wellhead', 'equipment', index, 'store', e.target.value)} /></td>
                                                <td><input value={row.remark} onChange={(e) => updateRow('wellhead', 'equipment', index, 'remark', e.target.value)} /></td>
                                                <td><button className="btn btn-remove" onClick={() => removeRow('wellhead', 'equipment', index)}>Remove</button></td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // Render the application
        ReactDOM.render(<WellDocumentationSystem />, document.getElementById('root'));
    </script>
</body>
</html>