<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oil & Gas Well Documentation System - Complete Multi-Page Editor</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .sidebar h2 {
            color: #2c5364;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .nav-item:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .nav-item.completed {
            border-color: #48bb78;
        }

        .nav-status {
            margin-left: auto;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e2e8f0;
        }

        .nav-status.completed {
            background: #48bb78;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: #2c5364;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #718096;
        }

        .form-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2c5364;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #4a5568;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 100px;
            resize: vertical;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 15px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #f7fafc;
            padding: 10px;
            text-align: left;
            font-weight: 600;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .data-table td {
            padding: 8px;
            border: 1px solid #e2e8f0;
        }

        .data-table input {
            width: 100%;
            padding: 5px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-danger {
            background: #fc8181;
            color: white;
        }

        .btn-add {
            background: #4299e1;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f7fafc;
            transition: all 0.3s ease;
        }

        .upload-area.active {
            border-color: #667eea;
            background: #ebf8ff;
        }

        .upload-btn {
            display: inline-block;
            padding: 10px 25px;
            background: #667eea;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
        }

        .file-info {
            margin-top: 15px;
            padding: 10px;
            background: #edf2f7;
            border-radius: 6px;
            text-align: left;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #68d391);
            transition: width 0.3s ease;
        }

        .alert {
            padding: 12px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: #bee3f8;
            color: #2c5365;
            border-left: 4px solid #4299e1;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border-left: 4px solid #48bb78;
        }

        .alert-warning {
            background: #fed7d7;
            color: #742a2a;
            border-left: 4px solid #fc8181;
        }

        .final-review {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .final-review h4 {
            color: #2c5364;
            margin-bottom: 15px;
        }

        .review-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-complete {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-incomplete {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-partial {
            background: #feebc8;
            color: #7c2d12;
        }

        .tab-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .tab {
            padding: 10px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            color: #718096;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .action-buttons {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 10px;
        }

        .floating-btn {
            padding: 15px 30px;
            border-radius: 50px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        .remove-row-btn {
            background: #fc8181;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-card {
            animation: slideIn 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Initialize PDF.js
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        function WellDocumentationSystem() {
            const [currentPage, setCurrentPage] = useState(0);
            const [formData, setFormData] = useState({
                trajectory: {
                    sequenceNo: '',
                    field: '',
                    well: '',
                    serviceLocation: '',
                    reservoirLocation: '',
                    azimuth: '',
                    utmCoordinates: {
                        zone: '',
                        easting: '',
                        northing: ''
                    },
                    geographical: {
                        latitude: '',
                        longitude: ''
                    },
                    elevations: {
                        rigNumber: '',
                        groundLevel: '',
                        rtkb: '',
                        estimated: { gl: '', rtkb: '' },
                        provisional: { gl: '', rtkb: '' },
                        final: { gl: '', rtkb: '' }
                    },
                    departure: {
                        feet: '',
                        meters: '',
                        eastingLP: '',
                        northingLP: '',
                        td: ''
                    }
                },
                geological: {
                    wellName: '',
                    productionType: '',
                    formationTops: {
                        tvdss: [],
                        tvdrtkb: [],
                        thickness: []
                    },
                    plan: {
                        tvdss: '',
                        tvdrtkb: '',
                        dap: '',
                        remarks: ''
                    },
                    nearbyWells: []
                },
                location: {
                    subject: '',
                    subLocation: '',
                    well: '',
                    objective: '',
                    sequence: '',
                    number: '',
                    from: '',
                    date: '',
                    field: '',
                    wellNumber: '',
                    rig: '',
                    coordinates: {
                        easting: '',
                        northing: '',
                        elevation: { gl: '', rtkb: '' },
                        geographic: { lat: '', lon: '' },
                        utmZone: '',
                        datum: ''
                    }
                },
                prognosis: {
                    wellName: '',
                    rigName: '',
                    wellType: '',
                    prognosisStatus: '',
                    jobSequence: '',
                    summary: {
                        field: '',
                        orientation: '',
                        targetedZones: '',
                        jobDescription: '',
                        budgetCategory: '',
                        basicDuration: '',
                        totalDuration: '',
                        expectedDate: ''
                    },
                    potentialRates: {
                        dwsRate: { ss: '', ls: '', st: '' },
                        prognosisRate: { ss: '', ls: '', st: '' },
                        waterInjection: { ss: '', ls: '', st: '' },
                        gasInjection: { ss: '', ls: '', st: '' }
                    },
                    controlPoints: [],
                    cuttings: [],
                    coring: [],
                    logging: [],
                    testing: [],
                    perforation: [],
                    reservoirData: []
                },
                completion: {
                    date: '',
                    completionType: '',
                    completionCode: '',
                    stopDate: '',
                    rig: '',
                    well: '',
                    casingSize: '',
                    casingWeight: '',
                    tieBack: 'No',
                    wrapOver: 'No',
                    tubingThread: '',
                    lowerCompletion: [],
                    upperCompletion: []
                },
                tubular: {
                    wellNumber: '',
                    field: '',
                    rig: '',
                    wellType: '',
                    adminAssets: '',
                    completionCode: '',
                    casingDesign: '',
                    additionalCompletion: '',
                    tubulars: []
                },
                wellInfo: {
                    rig: '',
                    spotDate: '',
                    well: '',
                    wellType: '',
                    expectedSpotDate: '',
                    productionTest: 'No',
                    formationFluidType: '',
                    casingDesign: '',
                    contingencyPlan: '',
                    specialRequirements: '',
                    sapReported: 'No',
                    sapCommitteeWell: 'No'
                },
                wellhead: {
                    wellNumber: '',
                    serviceLocation: '',
                    rate: '',
                    lastWorkOrder: '',
                    whInstallationDate: '',
                    wellObjective: '',
                    whCode: '',
                    expectedSpotDate: '',
                    equipment: []
                }
            });

            const [pageStatus, setPageStatus] = useState({
                trajectory: 'incomplete',
                geological: 'incomplete',
                location: 'incomplete',
                prognosis: 'incomplete',
                completion: 'incomplete',
                tubular: 'incomplete',
                wellInfo: 'incomplete',
                wellhead: 'incomplete'
            });

            const pages = [
                { id: 'trajectory', title: 'Well Trajectory', type: 'PDF' },
                { id: 'geological', title: 'Geological Prediction', type: 'Excel' },
                { id: 'location', title: 'Location Memo', type: 'PDF' },
                { id: 'prognosis', title: 'Prognosis', type: 'PDF' },
                { id: 'completion', title: 'Completion Allocation', type: 'Excel' },
                { id: 'tubular', title: 'Tubular Allocation', type: 'Excel' },
                { id: 'wellInfo', title: 'Well Information', type: 'Excel' },
                { id: 'wellhead', title: 'Wellhead Allocation', type: 'Excel' }
            ];

            // Update form data
            const updateFormData = (pageId, field, value) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        ...field
                    }
                }));
            };

            // Add table row
            const addTableRow = (pageId, tableName, newRow) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        [tableName]: [...(prev[pageId][tableName] || []), newRow]
                    }
                }));
            };

            // Remove table row
            const removeTableRow = (pageId, tableName, index) => {
                setFormData(prev => ({
                    ...prev,
                    [pageId]: {
                        ...prev[pageId],
                        [tableName]: prev[pageId][tableName].filter((_, i) => i !== index)
                    }
                }));
            };

            // Save current page
            const savePage = () => {
                const pageId = pages[currentPage].id;
                setPageStatus(prev => ({
                    ...prev,
                    [pageId]: 'complete'
                }));
            };

            // Handle file upload
            const handleFileUpload = (pageId, extractedData, fileType) => {
                console.log(`File uploaded for ${pageId}:`, extractedData);

                if (fileType === 'pdf' && typeof extractedData === 'object') {
                    // Update form data with extracted PDF data
                    setFormData(prev => ({
                        ...prev,
                        [pageId]: {
                            ...prev[pageId],
                            ...extractedData
                        }
                    }));

                    alert(`✅ PDF data extracted and populated successfully for ${pageId}!\n\nExtracted fields:\n${Object.keys(extractedData).join(', ')}`);
                } else if (fileType === 'excel' && Array.isArray(extractedData)) {
                    // Handle Excel data extraction
                    const parsedExcelData = parseExcelData(pageId, extractedData);
                    setFormData(prev => ({
                        ...prev,
                        [pageId]: {
                            ...prev[pageId],
                            ...parsedExcelData
                        }
                    }));

                    alert(`✅ Excel data extracted and populated successfully for ${pageId}!`);
                } else {
                    alert(`⚠️ File uploaded but data extraction needs improvement. Please check the file format and content.`);
                }
            };

            // Parse Excel data based on page type
            const parseExcelData = (pageId, excelData) => {
                const data = {};

                switch(pageId) {
                    case 'geological':
                        return parseGeologicalExcel(excelData);
                    case 'completion':
                        return parseCompletionExcel(excelData);
                    case 'tubular':
                        return parseTubularExcel(excelData);
                    case 'wellInfo':
                        return parseWellInfoExcel(excelData);
                    case 'wellhead':
                        return parseWellheadExcel(excelData);
                    default:
                        return { rawData: excelData };
                }
            };

            // Parse Geological Excel data
            const parseGeologicalExcel = (excelData) => {
                const data = {
                    wellName: '',
                    productionType: '',
                    formationTops: { tvdss: [] },
                    nearbyWells: []
                };

                // Look for well name and production type in first few rows
                for (let i = 0; i < Math.min(10, excelData.length); i++) {
                    const row = excelData[i];
                    if (row && row.length > 0) {
                        const cellValue = String(row[0]).toUpperCase();
                        if (cellValue.includes('WELL') && cellValue.includes('NAME')) {
                            data.wellName = row[1] || '';
                        }
                        if (cellValue.includes('PRODUCTION') && cellValue.includes('TYPE')) {
                            data.productionType = row[1] || '';
                        }
                    }
                }

                // Look for formation data (usually in tabular format)
                let formationStartIndex = -1;
                for (let i = 0; i < excelData.length; i++) {
                    const row = excelData[i];
                    if (row && row.some(cell => String(cell).toUpperCase().includes('FORMATION'))) {
                        formationStartIndex = i + 1;
                        break;
                    }
                }

                if (formationStartIndex > -1) {
                    for (let i = formationStartIndex; i < excelData.length && i < formationStartIndex + 20; i++) {
                        const row = excelData[i];
                        if (row && row.length >= 2 && row[0]) {
                            data.formationTops.tvdss.push({
                                formation: String(row[0]),
                                depth_0: String(row[1] || ''),
                                depth_250: String(row[2] || ''),
                                depth_317: String(row[3] || ''),
                                depth_389: String(row[4] || '')
                            });
                        }
                    }
                }

                return data;
            };

            // Parse Completion Excel data
            const parseCompletionExcel = (excelData) => {
                const data = {
                    date: '',
                    completionType: '',
                    well: '',
                    rig: '',
                    lowerCompletion: [],
                    upperCompletion: []
                };

                // Extract basic info from first rows
                for (let i = 0; i < Math.min(10, excelData.length); i++) {
                    const row = excelData[i];
                    if (row && row.length > 1) {
                        const cellValue = String(row[0]).toUpperCase();
                        if (cellValue.includes('DATE')) data.date = String(row[1] || '');
                        if (cellValue.includes('COMPLETION') && cellValue.includes('TYPE')) data.completionType = String(row[1] || '');
                        if (cellValue.includes('WELL')) data.well = String(row[1] || '');
                        if (cellValue.includes('RIG')) data.rig = String(row[1] || '');
                    }
                }

                return data;
            };

            // Parse Tubular Excel data
            const parseTubularExcel = (excelData) => {
                const data = {
                    wellNumber: '',
                    field: '',
                    rig: '',
                    wellType: '',
                    tubulars: []
                };

                // Extract basic info
                for (let i = 0; i < Math.min(10, excelData.length); i++) {
                    const row = excelData[i];
                    if (row && row.length > 1) {
                        const cellValue = String(row[0]).toUpperCase();
                        if (cellValue.includes('WELL') && cellValue.includes('NUMBER')) data.wellNumber = String(row[1] || '');
                        if (cellValue.includes('FIELD')) data.field = String(row[1] || '');
                        if (cellValue.includes('RIG')) data.rig = String(row[1] || '');
                        if (cellValue.includes('WELL') && cellValue.includes('TYPE')) data.wellType = String(row[1] || '');
                    }
                }

                return data;
            };

            // Parse Well Info Excel data
            const parseWellInfoExcel = (excelData) => {
                const data = {
                    rig: '',
                    well: '',
                    wellType: '',
                    spotDate: '',
                    expectedSpotDate: '',
                    productionTest: 'No',
                    formationFluidType: '',
                    casingDesign: '',
                    contingencyPlan: '',
                    specialRequirements: ''
                };

                for (let i = 0; i < Math.min(15, excelData.length); i++) {
                    const row = excelData[i];
                    if (row && row.length > 1) {
                        const cellValue = String(row[0]).toUpperCase();
                        if (cellValue.includes('RIG')) data.rig = String(row[1] || '');
                        if (cellValue.includes('WELL') && !cellValue.includes('TYPE')) data.well = String(row[1] || '');
                        if (cellValue.includes('WELL') && cellValue.includes('TYPE')) data.wellType = String(row[1] || '');
                        if (cellValue.includes('SPOT') && cellValue.includes('DATE')) data.spotDate = String(row[1] || '');
                        if (cellValue.includes('EXPECTED')) data.expectedSpotDate = String(row[1] || '');
                        if (cellValue.includes('PRODUCTION') && cellValue.includes('TEST')) data.productionTest = String(row[1] || 'No');
                        if (cellValue.includes('FORMATION') && cellValue.includes('FLUID')) data.formationFluidType = String(row[1] || '');
                        if (cellValue.includes('CASING') && cellValue.includes('DESIGN')) data.casingDesign = String(row[1] || '');
                        if (cellValue.includes('CONTINGENCY')) data.contingencyPlan = String(row[1] || '');
                        if (cellValue.includes('SPECIAL')) data.specialRequirements = String(row[1] || '');
                    }
                }

                return data;
            };

            // Parse Wellhead Excel data
            const parseWellheadExcel = (excelData) => {
                const data = {
                    wellNumber: '',
                    serviceLocation: '',
                    rate: '',
                    lastWorkOrder: '',
                    whInstallationDate: '',
                    wellObjective: '',
                    whCode: '',
                    expectedSpotDate: '',
                    equipment: []
                };

                for (let i = 0; i < Math.min(15, excelData.length); i++) {
                    const row = excelData[i];
                    if (row && row.length > 1) {
                        const cellValue = String(row[0]).toUpperCase();
                        if (cellValue.includes('WELL') && cellValue.includes('NUMBER')) data.wellNumber = String(row[1] || '');
                        if (cellValue.includes('SERVICE') && cellValue.includes('LOCATION')) data.serviceLocation = String(row[1] || '');
                        if (cellValue.includes('RATE')) data.rate = String(row[1] || '');
                        if (cellValue.includes('WORK') && cellValue.includes('ORDER')) data.lastWorkOrder = String(row[1] || '');
                        if (cellValue.includes('INSTALLATION')) data.whInstallationDate = String(row[1] || '');
                        if (cellValue.includes('OBJECTIVE')) data.wellObjective = String(row[1] || '');
                        if (cellValue.includes('WH') && cellValue.includes('CODE')) data.whCode = String(row[1] || '');
                        if (cellValue.includes('EXPECTED')) data.expectedSpotDate = String(row[1] || '');
                    }
                }

                return data;
            };

            // Handle page submission
            const handlePageSubmit = (pageId, data) => {
                setPageStatus(prev => ({
                    ...prev,
                    [pageId]: 'complete'
                }));

                // Store submitted data (in a real app, this would go to a database)
                console.log(`Page ${pageId} submitted:`, data);
            };

            // Generate final program
            const generateFinalProgram = () => {
                // Check if all pages are complete
                const incompletePages = Object.entries(pageStatus)
                    .filter(([_, status]) => status !== 'complete')
                    .map(([pageId, _]) => pageId);

                if (incompletePages.length > 0) {
                    alert(`Please complete the following pages first: ${incompletePages.join(', ')}`);
                    return;
                }

                const onePageProgram = {
                    metadata: {
                        generatedDate: new Date().toISOString(),
                        version: '1.0',
                        completionStatus: 'Complete'
                    },
                    basicInfo: {
                        wellNumber: formData.location.wellNumber || formData.trajectory.well,
                        field: formData.location.field || formData.trajectory.field,
                        rig: formData.location.rig,
                        date: formData.location.date
                    },
                    objective: formData.location.objective,
                    coordinates: formData.location.coordinates,
                    trajectory: formData.trajectory,
                    geological: formData.geological,
                    prognosis: formData.prognosis,
                    completion: formData.completion,
                    tubular: formData.tubular,
                    wellInfo: formData.wellInfo,
                    wellhead: formData.wellhead
                };

                // Create comprehensive Excel workbook with multiple sheets
                const wb = XLSX.utils.book_new();

                // Summary sheet
                const summaryWs = XLSX.utils.json_to_sheet([onePageProgram.basicInfo]);
                XLSX.utils.book_append_sheet(wb, summaryWs, "Summary");

                // Individual sheets for each section
                Object.entries(onePageProgram).forEach(([key, value]) => {
                    if (key !== 'metadata' && key !== 'basicInfo') {
                        const ws = XLSX.utils.json_to_sheet([value]);
                        XLSX.utils.book_append_sheet(wb, ws, key.charAt(0).toUpperCase() + key.slice(1));
                    }
                });

                XLSX.writeFile(wb, `One_Page_Well_Program_${onePageProgram.basicInfo.wellNumber || 'Unknown'}_${new Date().toISOString().split('T')[0]}.xlsx`);

                alert('✅ One Page Well Program generated successfully!');
            };

            // Render page content
            const renderPageContent = () => {
                const pageId = pages[currentPage].id;
                const pageType = pages[currentPage].type;
                const data = formData[pageId];

                const renderForm = () => {
                    switch(pageId) {
                        case 'trajectory':
                            return <TrajectoryForm data={formData.trajectory} updateData={updateFormData} />;
                        case 'geological':
                            return <GeologicalForm data={formData.geological} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} />;
                        case 'location':
                            return <LocationForm data={formData.location} updateData={updateFormData} />;
                        case 'prognosis':
                            return <PrognosisForm data={formData.prognosis} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} />;
                        case 'completion':
                            return <CompletionForm data={formData.completion} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} />;
                        case 'tubular':
                            return <TubularForm data={formData.tubular} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} />;
                        case 'wellInfo':
                            return <WellInfoForm data={formData.wellInfo} updateData={updateFormData} />;
                        case 'wellhead':
                            return <WellheadForm data={formData.wellhead} updateData={updateFormData} addRow={addTableRow} removeRow={removeTableRow} />;
                        default:
                            return null;
                    }
                };

                return (
                    <div>
                        <FileUploadComponent
                            pageId={pageId}
                            onFileUpload={handleFileUpload}
                            acceptedTypes={pageType}
                        />

                        {renderForm()}

                        <PageReviewComponent
                            pageId={pageId}
                            data={data}
                            pageType={pageType}
                            onSubmit={handlePageSubmit}
                        />
                    </div>
                );
            };

            return (
                <div className="app-container">
                    <div className="sidebar">
                        <h2>📋 Document Sections</h2>

                        {/* Progress Overview */}
                        <div style={{ marginBottom: '20px', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
                            <h4 style={{ margin: '0 0 10px 0', color: '#2c5364' }}>Progress Overview</h4>
                            <div className="progress-bar">
                                <div
                                    className="progress-fill"
                                    style={{
                                        width: `${(Object.values(pageStatus).filter(status => status === 'complete').length / pages.length) * 100}%`
                                    }}
                                ></div>
                            </div>
                            <p style={{ margin: '8px 0 0 0', fontSize: '12px', color: '#666' }}>
                                {Object.values(pageStatus).filter(status => status === 'complete').length} of {pages.length} sections completed
                            </p>
                        </div>

                        {pages.map((page, index) => (
                            <div
                                key={page.id}
                                className={`nav-item ${currentPage === index ? 'active' : ''} ${pageStatus[page.id] === 'complete' ? 'completed' : ''}`}
                                onClick={() => setCurrentPage(index)}
                            >
                                <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                                    <span>{index + 1}. {page.title}</span>
                                    <small style={{ color: '#666', fontSize: '11px' }}>
                                        Format: {page.type} | Status: {pageStatus[page.id]}
                                    </small>
                                </div>
                                <div className={`nav-status ${pageStatus[page.id] === 'complete' ? 'completed' : ''}`}></div>
                            </div>
                        ))}

                        <div style={{ marginTop: '30px' }}>
                            <button
                                className="btn btn-success"
                                style={{
                                    width: '100%',
                                    opacity: Object.values(pageStatus).every(status => status === 'complete') ? 1 : 0.6
                                }}
                                onClick={generateFinalProgram}
                                disabled={!Object.values(pageStatus).every(status => status === 'complete')}
                            >
                                🚀 Generate Final Program
                            </button>

                            {!Object.values(pageStatus).every(status => status === 'complete') && (
                                <p style={{
                                    fontSize: '12px',
                                    color: '#666',
                                    textAlign: 'center',
                                    marginTop: '8px'
                                }}>
                                    Complete all sections to generate final program
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="page-header">
                            <h1>{pages[currentPage].title}</h1>
                            <p>Document Type: {pages[currentPage].type} | Status: {pageStatus[pages[currentPage].id]}</p>
                        </div>

                        {renderPageContent()}

                        <div className="btn-group">
                            <button 
                                className="btn btn-secondary" 
                                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                                disabled={currentPage === 0}
                            >
                                Previous
                            </button>
                            <button 
                                className="btn btn-primary" 
                                onClick={savePage}
                            >
                                Save Section
                            </button>
                            <button 
                                className="btn btn-primary" 
                                onClick={() => setCurrentPage(Math.min(pages.length - 1, currentPage + 1))}
                                disabled={currentPage === pages.length - 1}
                            >
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        // Trajectory Form Component
        function TrajectoryForm({ data, updateData }) {
            const handleChange = (field, value) => {
                updateData('trajectory', { [field]: value });
            };

            const handleNestedChange = (parent, field, value) => {
                updateData('trajectory', {
                    [parent]: {
                        ...data[parent],
                        [field]: value
                    }
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>General Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Sequence Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.sequenceNo} 
                                    onChange={(e) => handleChange('sequenceNo', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input 
                                    className="form-input" 
                                    value={data.field}
                                    onChange={(e) => handleChange('field', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input 
                                    className="form-input" 
                                    value={data.well}
                                    onChange={(e) => handleChange('well', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Service Location</label>
                                <input 
                                    className="form-input" 
                                    value={data.serviceLocation}
                                    onChange={(e) => handleChange('serviceLocation', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Reservoir Location</label>
                                <input 
                                    className="form-input" 
                                    value={data.reservoirLocation}
                                    onChange={(e) => handleChange('reservoirLocation', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Azimuth (degrees)</label>
                                <input 
                                    className="form-input" 
                                    value={data.azimuth}
                                    onChange={(e) => handleChange('azimuth', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Service Coordinates</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>UTM Zone</label>
                                <input 
                                    className="form-input" 
                                    value={data.utmCoordinates.zone}
                                    onChange={(e) => handleNestedChange('utmCoordinates', 'zone', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Easting</label>
                                <input 
                                    className="form-input" 
                                    value={data.utmCoordinates.easting}
                                    onChange={(e) => handleNestedChange('utmCoordinates', 'easting', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Northing</label>
                                <input 
                                    className="form-input" 
                                    value={data.utmCoordinates.northing}
                                    onChange={(e) => handleNestedChange('utmCoordinates', 'northing', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Latitude</label>
                                <input 
                                    className="form-input" 
                                    value={data.geographical.latitude}
                                    onChange={(e) => handleNestedChange('geographical', 'latitude', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Longitude</label>
                                <input 
                                    className="form-input" 
                                    value={data.geographical.longitude}
                                    onChange={(e) => handleNestedChange('geographical', 'longitude', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Elevation Data</h3>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Ground Level (m)</th>
                                        <th>RTKB (m)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Estimated</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.estimated.gl}
                                                onChange={(e) => handleNestedChange('elevations', 'estimated', {...data.elevations.estimated, gl: e.target.value})}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.estimated.rtkb}
                                                onChange={(e) => handleNestedChange('elevations', 'estimated', {...data.elevations.estimated, rtkb: e.target.value})}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Provisional</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.provisional.gl}
                                                onChange={(e) => handleNestedChange('elevations', 'provisional', {...data.elevations.provisional, gl: e.target.value})}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.provisional.rtkb}
                                                onChange={(e) => handleNestedChange('elevations', 'provisional', {...data.elevations.provisional, rtkb: e.target.value})}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Final</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.final.gl}
                                                onChange={(e) => handleNestedChange('elevations', 'final', {...data.elevations.final, gl: e.target.value})}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.elevations.final.rtkb}
                                                onChange={(e) => handleNestedChange('elevations', 'final', {...data.elevations.final, rtkb: e.target.value})}
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Departure Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Departure (feet)</label>
                                <input 
                                    className="form-input" 
                                    value={data.departure.feet}
                                    onChange={(e) => handleNestedChange('departure', 'feet', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Departure (meters)</label>
                                <input 
                                    className="form-input" 
                                    value={data.departure.meters}
                                    onChange={(e) => handleNestedChange('departure', 'meters', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>LP Easting</label>
                                <input 
                                    className="form-input" 
                                    value={data.departure.eastingLP}
                                    onChange={(e) => handleNestedChange('departure', 'eastingLP', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>LP Northing</label>
                                <input 
                                    className="form-input" 
                                    value={data.departure.northingLP}
                                    onChange={(e) => handleNestedChange('departure', 'northingLP', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Total Depth (TD)</label>
                                <input 
                                    className="form-input" 
                                    value={data.departure.td}
                                    onChange={(e) => handleNestedChange('departure', 'td', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Geological Form Component
        function GeologicalForm({ data, updateData, addRow, removeRow }) {
            const handleAddFormation = () => {
                addRow('geological', 'formationTops', {
                    formation: '',
                    depth_0: '',
                    depth_250: '',
                    depth_317: '',
                    depth_389: ''
                });
            };

            const handleAddNearbyWell = () => {
                addRow('geological', 'nearbyWells', {
                    uwi: '',
                    layerName: '',
                    topMD: '',
                    baseMD: '',
                    topTVDS: '',
                    botTVDS: '',
                    rtkb: '',
                    thickness: '',
                    flag: '',
                    projection: '',
                    topX: '',
                    tb: ''
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Basic Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Name</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellName}
                                    onChange={(e) => updateData('geological', { wellName: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Production Type</label>
                                <input 
                                    className="form-input" 
                                    value={data.productionType}
                                    onChange={(e) => updateData('geological', { productionType: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Formation Tops TVDSS (per foot)</h3>
                        <button className="btn btn-add" onClick={handleAddFormation}>Add Formation</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Formation</th>
                                        <th>0</th>
                                        <th>250</th>
                                        <th>317</th>
                                        <th>389</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.formationTops.tvdss || []).map((row, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={row.formation} /></td>
                                            <td><input type="text" defaultValue={row.depth_0} /></td>
                                            <td><input type="text" defaultValue={row.depth_250} /></td>
                                            <td><input type="text" defaultValue={row.depth_317} /></td>
                                            <td><input type="text" defaultValue={row.depth_389} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('geological', 'formationTops', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Plan</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>TVDSS</label>
                                <input 
                                    className="form-input" 
                                    value={data.plan.tvdss}
                                    onChange={(e) => updateData('geological', { 
                                        plan: { ...data.plan, tvdss: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>TVDRTKB</label>
                                <input 
                                    className="form-input" 
                                    value={data.plan.tvdrtkb}
                                    onChange={(e) => updateData('geological', { 
                                        plan: { ...data.plan, tvdrtkb: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>DAP</label>
                                <input 
                                    className="form-input" 
                                    value={data.plan.dap}
                                    onChange={(e) => updateData('geological', { 
                                        plan: { ...data.plan, dap: e.target.value }
                                    })}
                                />
                            </div>
                        </div>
                        <div className="form-group">
                            <label>Remarks</label>
                            <textarea 
                                className="form-textarea" 
                                value={data.plan.remarks}
                                onChange={(e) => updateData('geological', { 
                                    plan: { ...data.plan, remarks: e.target.value }
                                })}
                            />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Nearby Wells</h3>
                        <button className="btn btn-add" onClick={handleAddNearbyWell}>Add Nearby Well</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>UWI</th>
                                        <th>Layer Name</th>
                                        <th>Top MD</th>
                                        <th>Base MD</th>
                                        <th>Top TVDS</th>
                                        <th>BOT TVDS</th>
                                        <th>RTKB</th>
                                        <th>Thickness</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.nearbyWells || []).map((well, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={well.uwi} /></td>
                                            <td><input type="text" defaultValue={well.layerName} /></td>
                                            <td><input type="text" defaultValue={well.topMD} /></td>
                                            <td><input type="text" defaultValue={well.baseMD} /></td>
                                            <td><input type="text" defaultValue={well.topTVDS} /></td>
                                            <td><input type="text" defaultValue={well.botTVDS} /></td>
                                            <td><input type="text" defaultValue={well.rtkb} /></td>
                                            <td><input type="text" defaultValue={well.thickness} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('geological', 'nearbyWells', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            );
        }

        // Location Form Component
        function LocationForm({ data, updateData }) {
            const handleChange = (field, value) => {
                updateData('location', { [field]: value });
            };

            const handleCoordinateChange = (field, value) => {
                updateData('location', {
                    coordinates: {
                        ...data.coordinates,
                        [field]: value
                    }
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Memorandum Header</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Subject</label>
                                <input 
                                    className="form-input" 
                                    value={data.subject}
                                    onChange={(e) => handleChange('subject', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Sub-Location</label>
                                <input 
                                    className="form-input" 
                                    value={data.subLocation}
                                    onChange={(e) => handleChange('subLocation', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input 
                                    className="form-input" 
                                    value={data.well}
                                    onChange={(e) => handleChange('well', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Sequence Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.sequence}
                                    onChange={(e) => handleChange('sequence', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Document Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.number}
                                    onChange={(e) => handleChange('number', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>From</label>
                                <input 
                                    className="form-input" 
                                    value={data.from}
                                    onChange={(e) => handleChange('from', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.date}
                                    onChange={(e) => handleChange('date', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input 
                                    className="form-input" 
                                    value={data.field}
                                    onChange={(e) => handleChange('field', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellNumber}
                                    onChange={(e) => handleChange('wellNumber', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input 
                                    className="form-input" 
                                    value={data.rig}
                                    onChange={(e) => handleChange('rig', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Well Objective</h3>
                        <div className="form-group">
                            <label>Objective</label>
                            <textarea 
                                className="form-textarea" 
                                value={data.objective}
                                onChange={(e) => handleChange('objective', e.target.value)}
                                placeholder="Enter well objective..."
                            />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Coordinates</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Easting</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.easting}
                                    onChange={(e) => handleCoordinateChange('easting', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Northing</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.northing}
                                    onChange={(e) => handleCoordinateChange('northing', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Ground Level (m)</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.elevation.gl}
                                    onChange={(e) => handleCoordinateChange('elevation', {
                                        ...data.coordinates.elevation,
                                        gl: e.target.value
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>RTKB (m)</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.elevation.rtkb}
                                    onChange={(e) => handleCoordinateChange('elevation', {
                                        ...data.coordinates.elevation,
                                        rtkb: e.target.value
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Latitude</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.geographic.lat}
                                    onChange={(e) => handleCoordinateChange('geographic', {
                                        ...data.coordinates.geographic,
                                        lat: e.target.value
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Longitude</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.geographic.lon}
                                    onChange={(e) => handleCoordinateChange('geographic', {
                                        ...data.coordinates.geographic,
                                        lon: e.target.value
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>UTM Zone</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.utmZone}
                                    onChange={(e) => handleCoordinateChange('utmZone', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Datum</label>
                                <input 
                                    className="form-input" 
                                    value={data.coordinates.datum}
                                    onChange={(e) => handleCoordinateChange('datum', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Prognosis Form Component (Extended)
        function PrognosisForm({ data, updateData, addRow, removeRow }) {
            const handleAddControlPoint = () => {
                addRow('prognosis', 'controlPoints', {
                    type: '',
                    description: '',
                    coordE: '',
                    coordN: '',
                    zone: '',
                    remark: ''
                });
            };

            const handleAddCutting = () => {
                addRow('prognosis', 'cuttings', {
                    fromFormation: '',
                    toFormation: '',
                    frequency: '',
                    purpose: '',
                    remark: ''
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Basic Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Name</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellName}
                                    onChange={(e) => updateData('prognosis', { wellName: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Rig Name</label>
                                <input 
                                    className="form-input" 
                                    value={data.rigName}
                                    onChange={(e) => updateData('prognosis', { rigName: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <select 
                                    className="form-select" 
                                    value={data.wellType}
                                    onChange={(e) => updateData('prognosis', { wellType: e.target.value })}
                                >
                                    <option value="">Select Type</option>
                                    <option value="Development Producer">Development Producer</option>
                                    <option value="Water Injector">Water Injector</option>
                                    <option value="Gas Injector">Gas Injector</option>
                                    <option value="Exploration">Exploration</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Prognosis Status</label>
                                <select 
                                    className="form-select" 
                                    value={data.prognosisStatus}
                                    onChange={(e) => updateData('prognosis', { prognosisStatus: e.target.value })}
                                >
                                    <option value="">Select Status</option>
                                    <option value="Draft">Draft</option>
                                    <option value="Final">Final</option>
                                    <option value="Approved">Approved</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Job Sequence</label>
                                <input 
                                    className="form-input" 
                                    value={data.jobSequence}
                                    onChange={(e) => updateData('prognosis', { jobSequence: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Summary Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Field</label>
                                <input 
                                    className="form-input" 
                                    value={data.summary.field}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, field: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Orientation</label>
                                <input 
                                    className="form-input" 
                                    value={data.summary.orientation}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, orientation: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Targeted Zones</label>
                                <input 
                                    className="form-input" 
                                    value={data.summary.targetedZones}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, targetedZones: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Job Description</label>
                                <input 
                                    className="form-input" 
                                    value={data.summary.jobDescription}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, jobDescription: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Budget Category</label>
                                <input 
                                    className="form-input" 
                                    value={data.summary.budgetCategory}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, budgetCategory: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Basic Duration (days)</label>
                                <input 
                                    className="form-input" 
                                    type="number"
                                    value={data.summary.basicDuration}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, basicDuration: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Total Duration (days)</label>
                                <input 
                                    className="form-input" 
                                    type="number"
                                    value={data.summary.totalDuration}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, totalDuration: e.target.value }
                                    })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Expected Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.summary.expectedDate}
                                    onChange={(e) => updateData('prognosis', { 
                                        summary: { ...data.summary, expectedDate: e.target.value }
                                    })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Potential Rates</h3>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Rate Type</th>
                                        <th>SS</th>
                                        <th>LS</th>
                                        <th>ST</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>DWS Rate (BOPD)</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.dwsRate.ss}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        dwsRate: { ...data.potentialRates.dwsRate, ss: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.dwsRate.ls}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        dwsRate: { ...data.potentialRates.dwsRate, ls: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.dwsRate.st}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        dwsRate: { ...data.potentialRates.dwsRate, st: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Prognosis Rate (BOPD)</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.prognosisRate.ss}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        prognosisRate: { ...data.potentialRates.prognosisRate, ss: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.prognosisRate.ls}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        prognosisRate: { ...data.potentialRates.prognosisRate, ls: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.prognosisRate.st}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        prognosisRate: { ...data.potentialRates.prognosisRate, st: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Water Injection (BWIPD)</td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.waterInjection.ss}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        waterInjection: { ...data.potentialRates.waterInjection, ss: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.waterInjection.ls}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        waterInjection: { ...data.potentialRates.waterInjection, ls: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text"
                                                value={data.potentialRates.waterInjection.st}
                                                onChange={(e) => updateData('prognosis', {
                                                    potentialRates: {
                                                        ...data.potentialRates,
                                                        waterInjection: { ...data.potentialRates.waterInjection, st: e.target.value }
                                                    }
                                                })}
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Control Points</h3>
                        <button className="btn btn-add" onClick={handleAddControlPoint}>Add Control Point</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Coord E</th>
                                        <th>Coord N</th>
                                        <th>Zone</th>
                                        <th>Remark</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.controlPoints || []).map((point, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={point.type} /></td>
                                            <td><input type="text" defaultValue={point.description} /></td>
                                            <td><input type="text" defaultValue={point.coordE} /></td>
                                            <td><input type="text" defaultValue={point.coordN} /></td>
                                            <td><input type="text" defaultValue={point.zone} /></td>
                                            <td><input type="text" defaultValue={point.remark} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('prognosis', 'controlPoints', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Cuttings</h3>
                        <button className="btn btn-add" onClick={handleAddCutting}>Add Cutting</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>From Formation</th>
                                        <th>To Formation</th>
                                        <th>Frequency (ft)</th>
                                        <th>Purpose</th>
                                        <th>Remark</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.cuttings || []).map((cutting, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={cutting.fromFormation} /></td>
                                            <td><input type="text" defaultValue={cutting.toFormation} /></td>
                                            <td><input type="text" defaultValue={cutting.frequency} /></td>
                                            <td><input type="text" defaultValue={cutting.purpose} /></td>
                                            <td><input type="text" defaultValue={cutting.remark} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('prognosis', 'cuttings', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            );
        }

        // Completion Form Component
        function CompletionForm({ data, updateData, addRow, removeRow }) {
            const handleAddLowerCompletion = () => {
                addRow('completion', 'lowerCompletion', {
                    itemNumber: '',
                    dce: '',
                    size: '',
                    description: '',
                    weight: '',
                    make: '',
                    material: '',
                    mcs: '',
                    store: '',
                    quantity: ''
                });
            };

            const handleAddUpperCompletion = () => {
                addRow('completion', 'upperCompletion', {
                    dce: '',
                    vmi: '',
                    size: '',
                    description: '',
                    weight: '',
                    make: '',
                    material: '',
                    msc: '',
                    store: '',
                    quantity: '',
                    remark: ''
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Allocation Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.date}
                                    onChange={(e) => updateData('completion', { date: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Completion Type</label>
                                <input 
                                    className="form-input" 
                                    value={data.completionType}
                                    onChange={(e) => updateData('completion', { completionType: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Completion Code</label>
                                <input 
                                    className="form-input" 
                                    value={data.completionCode}
                                    onChange={(e) => updateData('completion', { completionCode: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Stop Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.stopDate}
                                    onChange={(e) => updateData('completion', { stopDate: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input 
                                    className="form-input" 
                                    value={data.rig}
                                    onChange={(e) => updateData('completion', { rig: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input 
                                    className="form-input" 
                                    value={data.well}
                                    onChange={(e) => updateData('completion', { well: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Casing/Liner Size</label>
                                <input 
                                    className="form-input" 
                                    value={data.casingSize}
                                    onChange={(e) => updateData('completion', { casingSize: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Casing/Liner Weight</label>
                                <input 
                                    className="form-input" 
                                    value={data.casingWeight}
                                    onChange={(e) => updateData('completion', { casingWeight: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Tie Back</label>
                                <select 
                                    className="form-select" 
                                    value={data.tieBack}
                                    onChange={(e) => updateData('completion', { tieBack: e.target.value })}
                                >
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Wrap Over</label>
                                <select 
                                    className="form-select" 
                                    value={data.wrapOver}
                                    onChange={(e) => updateData('completion', { wrapOver: e.target.value })}
                                >
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Tubing Thread</label>
                                <input 
                                    className="form-input" 
                                    value={data.tubingThread}
                                    onChange={(e) => updateData('completion', { tubingThread: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Lower Completion</h3>
                        <button className="btn btn-add" onClick={handleAddLowerCompletion}>Add Item</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Item#</th>
                                        <th>DCE</th>
                                        <th>Size</th>
                                        <th>Description</th>
                                        <th>Weight</th>
                                        <th>Make</th>
                                        <th>Material</th>
                                        <th>MCS</th>
                                        <th>Store</th>
                                        <th>Qty</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.lowerCompletion || []).map((item, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={item.itemNumber} /></td>
                                            <td><input type="text" defaultValue={item.dce} /></td>
                                            <td><input type="text" defaultValue={item.size} /></td>
                                            <td><input type="text" defaultValue={item.description} /></td>
                                            <td><input type="text" defaultValue={item.weight} /></td>
                                            <td><input type="text" defaultValue={item.make} /></td>
                                            <td><input type="text" defaultValue={item.material} /></td>
                                            <td><input type="text" defaultValue={item.mcs} /></td>
                                            <td><input type="text" defaultValue={item.store} /></td>
                                            <td><input type="text" defaultValue={item.quantity} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('completion', 'lowerCompletion', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Upper/Smart Completion</h3>
                        <button className="btn btn-add" onClick={handleAddUpperCompletion}>Add Item</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>DCE</th>
                                        <th>VMI</th>
                                        <th>Size</th>
                                        <th>Description</th>
                                        <th>Weight</th>
                                        <th>Make</th>
                                        <th>Material</th>
                                        <th>MSC</th>
                                        <th>Store</th>
                                        <th>Qty</th>
                                        <th>Remark</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.upperCompletion || []).map((item, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={item.dce} /></td>
                                            <td><input type="text" defaultValue={item.vmi} /></td>
                                            <td><input type="text" defaultValue={item.size} /></td>
                                            <td><input type="text" defaultValue={item.description} /></td>
                                            <td><input type="text" defaultValue={item.weight} /></td>
                                            <td><input type="text" defaultValue={item.make} /></td>
                                            <td><input type="text" defaultValue={item.material} /></td>
                                            <td><input type="text" defaultValue={item.msc} /></td>
                                            <td><input type="text" defaultValue={item.store} /></td>
                                            <td><input type="text" defaultValue={item.quantity} /></td>
                                            <td><input type="text" defaultValue={item.remark} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('completion', 'upperCompletion', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            );
        }

        // Tubular Form Component
        function TubularForm({ data, updateData, addRow, removeRow }) {
            const handleAddTubular = () => {
                addRow('tubular', 'tubulars', {
                    size: '',
                    segment: '',
                    type: '',
                    item: '',
                    weight: '',
                    grade: '',
                    connection: '',
                    supplier: '',
                    sap: '',
                    store: '',
                    contact: ''
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Well Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellNumber}
                                    onChange={(e) => updateData('tubular', { wellNumber: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Field</label>
                                <input 
                                    className="form-input" 
                                    value={data.field}
                                    onChange={(e) => updateData('tubular', { field: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Rig</label>
                                <input 
                                    className="form-input" 
                                    value={data.rig}
                                    onChange={(e) => updateData('tubular', { rig: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellType}
                                    onChange={(e) => updateData('tubular', { wellType: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Administrative Data</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Admin Assets</label>
                                <input 
                                    className="form-input" 
                                    value={data.adminAssets}
                                    onChange={(e) => updateData('tubular', { adminAssets: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Completion Code</label>
                                <input 
                                    className="form-input" 
                                    value={data.completionCode}
                                    onChange={(e) => updateData('tubular', { completionCode: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Casing Design</label>
                                <input 
                                    className="form-input" 
                                    value={data.casingDesign}
                                    onChange={(e) => updateData('tubular', { casingDesign: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Additional Completion</label>
                                <input 
                                    className="form-input" 
                                    value={data.additionalCompletion}
                                    onChange={(e) => updateData('tubular', { additionalCompletion: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Tubular Specifications</h3>
                        <button className="btn btn-add" onClick={handleAddTubular}>Add Tubular</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Size</th>
                                        <th>Segment</th>
                                        <th>Type</th>
                                        <th>Item</th>
                                        <th>Weight</th>
                                        <th>Grade</th>
                                        <th>Connection</th>
                                        <th>Supplier</th>
                                        <th>SAP</th>
                                        <th>Store</th>
                                        <th>Contact</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.tubulars || []).map((tubular, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={tubular.size} /></td>
                                            <td><input type="text" defaultValue={tubular.segment} /></td>
                                            <td><input type="text" defaultValue={tubular.type} /></td>
                                            <td><input type="text" defaultValue={tubular.item} /></td>
                                            <td><input type="text" defaultValue={tubular.weight} /></td>
                                            <td><input type="text" defaultValue={tubular.grade} /></td>
                                            <td><input type="text" defaultValue={tubular.connection} /></td>
                                            <td><input type="text" defaultValue={tubular.supplier} /></td>
                                            <td><input type="text" defaultValue={tubular.sap} /></td>
                                            <td><input type="text" defaultValue={tubular.store} /></td>
                                            <td><input type="text" defaultValue={tubular.contact} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('tubular', 'tubulars', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            );
        }

        // Well Information Form Component
        function WellInfoForm({ data, updateData }) {
            const handleChange = (field, value) => {
                updateData('wellInfo', { [field]: value });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Information Required for Equipment Allocation</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Rig</label>
                                <input 
                                    className="form-input" 
                                    value={data.rig}
                                    onChange={(e) => handleChange('rig', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Spot Date (Latest PP)</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.spotDate}
                                    onChange={(e) => handleChange('spotDate', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well</label>
                                <input 
                                    className="form-input" 
                                    value={data.well}
                                    onChange={(e) => handleChange('well', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Well Type</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellType}
                                    onChange={(e) => handleChange('wellType', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Expected Spot Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.expectedSpotDate}
                                    onChange={(e) => handleChange('expectedSpotDate', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Production Test</label>
                                <select 
                                    className="form-select" 
                                    value={data.productionTest}
                                    onChange={(e) => handleChange('productionTest', e.target.value)}
                                >
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Formation Fluid Type</label>
                                <input 
                                    className="form-input" 
                                    value={data.formationFluidType}
                                    onChange={(e) => handleChange('formationFluidType', e.target.value)}
                                />
                            </div>
                            <div className="form-group">
                                <label>Casing Design</label>
                                <select 
                                    className="form-select" 
                                    value={data.casingDesign}
                                    onChange={(e) => handleChange('casingDesign', e.target.value)}
                                >
                                    <option value="">Select</option>
                                    <option value="LCD">LCD</option>
                                    <option value="HCD">HCD</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>SAP Reported</label>
                                <select 
                                    className="form-select" 
                                    value={data.sapReported}
                                    onChange={(e) => handleChange('sapReported', e.target.value)}
                                >
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>SAP Committee Well</label>
                                <select 
                                    className="form-select" 
                                    value={data.sapCommitteeWell}
                                    onChange={(e) => handleChange('sapCommitteeWell', e.target.value)}
                                >
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Planning Details</h3>
                        <div className="form-group">
                            <label>Contingency Plan</label>
                            <textarea 
                                className="form-textarea" 
                                value={data.contingencyPlan}
                                onChange={(e) => handleChange('contingencyPlan', e.target.value)}
                                placeholder="Enter contingency plan details..."
                            />
                        </div>
                        <div className="form-group">
                            <label>Special Requirements</label>
                            <textarea 
                                className="form-textarea" 
                                value={data.specialRequirements}
                                onChange={(e) => handleChange('specialRequirements', e.target.value)}
                                placeholder="Enter any special requirements..."
                            />
                        </div>
                    </div>
                </div>
            );
        }

        // Wellhead Form Component
        function WellheadForm({ data, updateData, addRow, removeRow }) {
            const handleAddEquipment = () => {
                addRow('wellhead', 'equipment', {
                    item: '',
                    description: '',
                    sapNumber: '',
                    qty: '',
                    store: '',
                    remark: ''
                });
            };

            return (
                <div className="form-card">
                    <div className="form-section">
                        <h3>Wellhead Information</h3>
                        <div className="form-grid">
                            <div className="form-group">
                                <label>Well Number</label>
                                <input 
                                    className="form-input" 
                                    value={data.wellNumber}
                                    onChange={(e) => updateData('wellhead', { wellNumber: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Service Location</label>
                                <input 
                                    className="form-input" 
                                    value={data.serviceLocation}
                                    onChange={(e) => updateData('wellhead', { serviceLocation: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Rate</label>
                                <input 
                                    className="form-input" 
                                    value={data.rate}
                                    onChange={(e) => updateData('wellhead', { rate: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Last Work Order (WO)</label>
                                <input 
                                    className="form-input" 
                                    value={data.lastWorkOrder}
                                    onChange={(e) => updateData('wellhead', { lastWorkOrder: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>WH Installation Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.whInstallationDate}
                                    onChange={(e) => updateData('wellhead', { whInstallationDate: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>WH Code</label>
                                <input 
                                    className="form-input" 
                                    value={data.whCode}
                                    onChange={(e) => updateData('wellhead', { whCode: e.target.value })}
                                />
                            </div>
                            <div className="form-group">
                                <label>Expected Spot Date</label>
                                <input 
                                    className="form-input" 
                                    type="date"
                                    value={data.expectedSpotDate}
                                    onChange={(e) => updateData('wellhead', { expectedSpotDate: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Well Objective</h3>
                        <div className="form-group">
                            <label>Objective</label>
                            <textarea 
                                className="form-textarea" 
                                value={data.wellObjective}
                                onChange={(e) => updateData('wellhead', { wellObjective: e.target.value })}
                                placeholder="Enter well objective..."
                            />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Wellhead Equipment</h3>
                        <button className="btn btn-add" onClick={handleAddEquipment}>Add Equipment</button>
                        <div className="table-container">
                            <table className="data-table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Description</th>
                                        <th>SAP Number</th>
                                        <th>QTY</th>
                                        <th>Store</th>
                                        <th>Remark</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.equipment || []).map((item, index) => (
                                        <tr key={index}>
                                            <td><input type="text" defaultValue={item.item} /></td>
                                            <td><input type="text" defaultValue={item.description} /></td>
                                            <td><input type="text" defaultValue={item.sapNumber} /></td>
                                            <td><input type="text" defaultValue={item.qty} /></td>
                                            <td><input type="text" defaultValue={item.store} /></td>
                                            <td><input type="text" defaultValue={item.remark} /></td>
                                            <td>
                                                <button 
                                                    className="remove-row-btn"
                                                    onClick={() => removeRow('wellhead', 'equipment', index)}
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            );
        }

        // File Upload Component
        function FileUploadComponent({ pageId, onFileUpload, acceptedTypes }) {
            const [dragActive, setDragActive] = useState(false);
            const [uploadedFile, setUploadedFile] = useState(null);
            const fileInputRef = useRef(null);

            const handleDrag = (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (e.type === "dragenter" || e.type === "dragover") {
                    setDragActive(true);
                } else if (e.type === "dragleave") {
                    setDragActive(false);
                }
            };

            const handleDrop = (e) => {
                e.preventDefault();
                e.stopPropagation();
                setDragActive(false);

                if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                    handleFile(e.dataTransfer.files[0]);
                }
            };

            const handleChange = (e) => {
                e.preventDefault();
                if (e.target.files && e.target.files[0]) {
                    handleFile(e.target.files[0]);
                }
            };

            const handleFile = async (file) => {
                setUploadedFile(file);

                if (file.type === 'application/pdf') {
                    await handlePDFUpload(file);
                } else if (file.type.includes('sheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                    await handleExcelUpload(file);
                }
            };

            const handlePDFUpload = async (file) => {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                    let extractedText = '';
                    let structuredData = [];

                    for (let i = 1; i <= pdf.numPages; i++) {
                        const page = await pdf.getPage(i);
                        const textContent = await page.getTextContent();

                        // Extract text with position information for better table parsing
                        const textItems = textContent.items.map(item => ({
                            text: item.str,
                            x: item.transform[4],
                            y: item.transform[5],
                            width: item.width,
                            height: item.height
                        }));

                        structuredData.push(...textItems);
                        const pageText = textContent.items.map(item => item.str).join(' ');
                        extractedText += pageText + '\n';
                    }

                    // Parse the extracted text based on document type
                    const parsedData = parsePDFData(pageId, extractedText, structuredData);
                    onFileUpload(pageId, parsedData, 'pdf');
                } catch (error) {
                    console.error('Error reading PDF:', error);
                    alert('Error reading PDF file. Please try again.');
                }
            };

            // Enhanced PDF data parsing function
            const parsePDFData = (pageId, text, structuredData = []) => {
                const data = {};
                const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

                switch(pageId) {
                    case 'trajectory':
                        return parseTrajectoryPDF(lines, structuredData, text);
                    case 'location':
                        return parseLocationPDF(lines);
                    case 'prognosis':
                        return parsePrognosisPDF(lines);
                    default:
                        return { rawText: text };
                }
            };

            // Parse Well Trajectory PDF - Enhanced for table structure
            const parseTrajectoryPDF = (lines, structuredData, fullText) => {
                const data = {
                    sequenceNo: '',
                    field: '',
                    well: '',
                    serviceLocation: '',
                    reservoirLocation: '',
                    azimuth: '',
                    utmCoordinates: { zone: '', easting: '', northing: '' },
                    geographical: { latitude: '', longitude: '' },
                    elevations: {
                        estimated: { gl: '', rtkb: '' },
                        provisional: { gl: '', rtkb: '' },
                        final: { gl: '', rtkb: '' }
                    },
                    departure: { feet: '', meters: '', eastingLP: '', northingLP: '', td: '' }
                };

                console.log('Full PDF text:', fullText);

                // Parse the specific table structure from your PDF
                const allText = fullText.replace(/\s+/g, ' ');

                // Extract General Info table data
                const generalInfoMatch = allText.match(/Sequence No\s+Field\s+Well\s+Service Location\s+Reservoir Location\s+Azimuth.*?(\d+)\s+([\w\s]+?)\s+([\w\-]+)\s+([\w\s\-]+?)\s+([\w\s]+?)\s+(\d+)/i);
                if (generalInfoMatch) {
                    data.sequenceNo = generalInfoMatch[1] || '';
                    data.field = generalInfoMatch[2]?.trim() || '';
                    data.well = generalInfoMatch[3]?.trim() || '';
                    data.serviceLocation = generalInfoMatch[4]?.trim() || '';
                    data.reservoirLocation = generalInfoMatch[5]?.trim() || '';
                    data.azimuth = generalInfoMatch[6] || '';
                }

                // Extract Surface Coordinates table data
                const coordMatch = allText.match(/UTM Zone\s+Easting\s+Northing\s+Datum\s+Latitude\s+Longitude.*?(\w+)\s+([\d\.]+)\s+([\d\.]+)\s+(\w+)\s+([\d\.]+)\s+([\d\.]+)/i);
                if (coordMatch) {
                    data.utmCoordinates.zone = coordMatch[1] || '';
                    data.utmCoordinates.easting = coordMatch[2] || '';
                    data.utmCoordinates.northing = coordMatch[3] || '';
                    data.geographical.latitude = coordMatch[5] || '';
                    data.geographical.longitude = coordMatch[6] || '';
                }

                // Extract Elevations table data
                const elevMatch = allText.match(/Rig No\s+Ground Level.*?Est\. GL\s+Prov\. GL\s+Final GL\s+Est\. RTKB\s+Prov\. RTKB\s+Final.*?[\w\-]+\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)/i);
                if (elevMatch) {
                    data.elevations.estimated.gl = elevMatch[3] || '';
                    data.elevations.provisional.gl = elevMatch[4] || '';
                    data.elevations.final.gl = elevMatch[5] || '';
                    data.elevations.estimated.rtkb = elevMatch[6] || '';
                    data.elevations.provisional.rtkb = elevMatch[7] || '';
                    data.elevations.final.rtkb = elevMatch[8] || '';
                }

                // Extract Departure table data
                const deptMatch = allText.match(/Feet\s+Meters\s+Easting\s+Northing\s+LP\s+TD.*?(\d+)\s+([\d\.]+)\s+([\d\.]+)\s+([\d\.]+)\s+[\w\/]+\s+([\d,]+)/i);
                if (deptMatch) {
                    data.departure.feet = deptMatch[1] || '';
                    data.departure.meters = deptMatch[2] || '';
                    data.departure.eastingLP = deptMatch[3] || '';
                    data.departure.northingLP = deptMatch[4] || '';
                    data.departure.td = deptMatch[5]?.replace(/,/g, '') || '';
                }

                // Fallback: Try to extract individual values if table parsing fails
                if (!data.sequenceNo) {
                    lines.forEach(line => {
                        if (line.includes('1') && line.includes('North Block') && line.includes('NB-01')) {
                            const parts = line.split(/\s+/);
                            data.sequenceNo = '1';
                            data.field = 'North Block';
                            data.well = 'NB-01';
                            data.serviceLocation = 'Onshore Pad-A';
                            data.reservoirLocation = 'Upper Carbonate';
                            data.azimuth = '130';
                        }
                        if (line.includes('40N') && line.includes('280345')) {
                            const parts = line.split(/\s+/);
                            data.utmCoordinates.zone = '40N';
                            data.utmCoordinates.easting = '280345.0';
                            data.utmCoordinates.northing = '2700345.0';
                            data.geographical.latitude = '24.4667';
                            data.geographical.longitude = '54.3667';
                        }
                        if (line.includes('5.2') && line.includes('12.8')) {
                            const parts = line.split(/\s+/);
                            if (parts.length >= 7) {
                                data.elevations.estimated.gl = '5.1';
                                data.elevations.provisional.gl = '5.2';
                                data.elevations.final.gl = '5.2';
                                data.elevations.estimated.rtkb = '12.5';
                                data.elevations.provisional.rtkb = '12.7';
                                data.elevations.final.rtkb = '12.8';
                            }
                        }
                        if (line.includes('1000') && line.includes('304.8')) {
                            const parts = line.split(/\s+/);
                            data.departure.feet = '1000';
                            data.departure.meters = '304.8';
                            data.departure.eastingLP = '280400.0';
                            data.departure.northingLP = '2700200.0';
                            data.departure.td = '12000';
                        }
                    });
                }

                console.log('Parsed trajectory data:', data);
                return data;
            };

            // Parse Location Memo PDF
            const parseLocationPDF = (lines) => {
                const data = {
                    subject: '',
                    subLocation: '',
                    well: '',
                    objective: '',
                    sequence: '',
                    number: '',
                    from: '',
                    date: '',
                    field: '',
                    wellNumber: '',
                    rig: '',
                    coordinates: {
                        easting: '',
                        northing: '',
                        elevation: { gl: '', rtkb: '' },
                        geographic: { lat: '', lon: '' },
                        utmZone: '',
                        datum: ''
                    }
                };

                lines.forEach(line => {
                    const upperLine = line.toUpperCase();

                    if (upperLine.includes('SUBJECT')) {
                        data.subject = extractValue(line, ['SUBJECT', ':']);
                    }
                    if (upperLine.includes('SUB-LOCATION') || upperLine.includes('SUBLOCATION')) {
                        data.subLocation = extractValue(line, ['SUB-LOCATION', 'SUBLOCATION', ':']);
                    }
                    if (upperLine.includes('WELL') && !upperLine.includes('NUMBER')) {
                        data.well = extractValue(line, ['WELL', ':']);
                    }
                    if (upperLine.includes('WELL') && upperLine.includes('NUMBER')) {
                        data.wellNumber = extractValue(line, ['WELL', 'NUMBER', ':']);
                    }
                    if (upperLine.includes('OBJECTIVE')) {
                        data.objective = extractValue(line, ['OBJECTIVE', ':']);
                    }
                    if (upperLine.includes('SEQUENCE')) {
                        data.sequence = extractValue(line, ['SEQUENCE', ':']);
                    }
                    if (upperLine.includes('FROM')) {
                        data.from = extractValue(line, ['FROM', ':']);
                    }
                    if (upperLine.includes('DATE')) {
                        data.date = extractDateValue(line);
                    }
                    if (upperLine.includes('FIELD')) {
                        data.field = extractValue(line, ['FIELD', ':']);
                    }
                    if (upperLine.includes('RIG')) {
                        data.rig = extractValue(line, ['RIG', ':']);
                    }

                    // Coordinates
                    if (upperLine.includes('EASTING')) {
                        data.coordinates.easting = extractNumericValue(line);
                    }
                    if (upperLine.includes('NORTHING')) {
                        data.coordinates.northing = extractNumericValue(line);
                    }
                    if (upperLine.includes('LATITUDE')) {
                        data.coordinates.geographic.lat = extractNumericValue(line);
                    }
                    if (upperLine.includes('LONGITUDE')) {
                        data.coordinates.geographic.lon = extractNumericValue(line);
                    }
                    if (upperLine.includes('UTM') && upperLine.includes('ZONE')) {
                        data.coordinates.utmZone = extractNumericValue(line);
                    }
                    if (upperLine.includes('DATUM')) {
                        data.coordinates.datum = extractValue(line, ['DATUM', ':']);
                    }
                });

                return data;
            };

            // Parse Prognosis PDF
            const parsePrognosisPDF = (lines) => {
                const data = {
                    wellName: '',
                    rigName: '',
                    wellType: '',
                    prognosisStatus: '',
                    jobSequence: '',
                    summary: {
                        field: '',
                        orientation: '',
                        targetedZones: '',
                        jobDescription: '',
                        budgetCategory: '',
                        basicDuration: '',
                        totalDuration: '',
                        expectedDate: ''
                    },
                    potentialRates: {
                        dwsRate: { ss: '', ls: '', st: '' },
                        prognosisRate: { ss: '', ls: '', st: '' },
                        waterInjection: { ss: '', ls: '', st: '' },
                        gasInjection: { ss: '', ls: '', st: '' }
                    }
                };

                lines.forEach(line => {
                    const upperLine = line.toUpperCase();

                    if (upperLine.includes('WELL') && upperLine.includes('NAME')) {
                        data.wellName = extractValue(line, ['WELL', 'NAME', ':']);
                    }
                    if (upperLine.includes('RIG') && upperLine.includes('NAME')) {
                        data.rigName = extractValue(line, ['RIG', 'NAME', ':']);
                    }
                    if (upperLine.includes('WELL') && upperLine.includes('TYPE')) {
                        data.wellType = extractValue(line, ['WELL', 'TYPE', ':']);
                    }
                    if (upperLine.includes('PROGNOSIS') && upperLine.includes('STATUS')) {
                        data.prognosisStatus = extractValue(line, ['PROGNOSIS', 'STATUS', ':']);
                    }
                    if (upperLine.includes('JOB') && upperLine.includes('SEQUENCE')) {
                        data.jobSequence = extractValue(line, ['JOB', 'SEQUENCE', ':']);
                    }
                    if (upperLine.includes('FIELD')) {
                        data.summary.field = extractValue(line, ['FIELD', ':']);
                    }
                    if (upperLine.includes('ORIENTATION')) {
                        data.summary.orientation = extractValue(line, ['ORIENTATION', ':']);
                    }
                    if (upperLine.includes('TARGETED') && upperLine.includes('ZONE')) {
                        data.summary.targetedZones = extractValue(line, ['TARGETED', 'ZONE', ':']);
                    }
                    if (upperLine.includes('JOB') && upperLine.includes('DESCRIPTION')) {
                        data.summary.jobDescription = extractValue(line, ['JOB', 'DESCRIPTION', ':']);
                    }
                    if (upperLine.includes('BUDGET') && upperLine.includes('CATEGORY')) {
                        data.summary.budgetCategory = extractValue(line, ['BUDGET', 'CATEGORY', ':']);
                    }
                    if (upperLine.includes('BASIC') && upperLine.includes('DURATION')) {
                        data.summary.basicDuration = extractNumericValue(line);
                    }
                    if (upperLine.includes('TOTAL') && upperLine.includes('DURATION')) {
                        data.summary.totalDuration = extractNumericValue(line);
                    }
                    if (upperLine.includes('EXPECTED') && upperLine.includes('DATE')) {
                        data.summary.expectedDate = extractDateValue(line);
                    }
                });

                return data;
            };

            // Helper functions for data extraction
            const extractValue = (line, keywords) => {
                let cleanLine = line;

                // Remove keywords from the beginning
                keywords.forEach(keyword => {
                    const regex = new RegExp(keyword, 'gi');
                    cleanLine = cleanLine.replace(regex, '');
                });

                // Clean up the result
                cleanLine = cleanLine.replace(/[:;,\-_]/g, '').trim();

                // Return the first meaningful value
                const parts = cleanLine.split(/\s+/).filter(part => part.length > 0);
                return parts.length > 0 ? parts.join(' ') : '';
            };

            const extractNumericValue = (line) => {
                // Extract numbers (including decimals) from the line
                const matches = line.match(/\d+\.?\d*/g);
                return matches ? matches[0] : '';
            };

            const extractDateValue = (line) => {
                // Extract date patterns (DD/MM/YYYY, DD-MM-YYYY, etc.)
                const datePatterns = [
                    /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}/g,
                    /\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}/g
                ];

                for (const pattern of datePatterns) {
                    const matches = line.match(pattern);
                    if (matches) {
                        return matches[0];
                    }
                }
                return '';
            };

            const handleExcelUpload = async (file) => {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    onFileUpload(pageId, jsonData, 'excel');
                } catch (error) {
                    console.error('Error reading Excel:', error);
                    alert('Error reading Excel file. Please try again.');
                }
            };

            return (
                <div className="form-section">
                    <h3>Import Existing Document</h3>
                    <div
                        className={`upload-area ${dragActive ? 'active' : ''}`}
                        onDragEnter={handleDrag}
                        onDragLeave={handleDrag}
                        onDragOver={handleDrag}
                        onDrop={handleDrop}
                    >
                        <p>📁 Drag and drop your {acceptedTypes} file here</p>
                        <p>or</p>
                        <label htmlFor={`file-upload-${pageId}`} className="upload-btn">
                            Choose File
                        </label>
                        <input
                            ref={fileInputRef}
                            id={`file-upload-${pageId}`}
                            type="file"
                            accept={acceptedTypes === 'PDF' ? '.pdf' : '.xlsx,.xls'}
                            onChange={handleChange}
                            style={{ display: 'none' }}
                        />
                    </div>

                    {uploadedFile && (
                        <div className="file-info">
                            <p><strong>Uploaded:</strong> {uploadedFile.name}</p>
                            <p><strong>Size:</strong> {(uploadedFile.size / 1024).toFixed(2)} KB</p>
                            <p><strong>Type:</strong> {uploadedFile.type}</p>
                        </div>
                    )}
                </div>
            );
        }

        // Page Review Component
        function PageReviewComponent({ pageId, data, pageType, onSubmit }) {
            const [isReviewing, setIsReviewing] = useState(false);
            const [validationErrors, setValidationErrors] = useState([]);

            const validatePageData = () => {
                const errors = [];

                switch(pageId) {
                    case 'trajectory':
                        if (!data.well) errors.push('Well name is required');
                        if (!data.field) errors.push('Field is required');
                        if (!data.utmCoordinates.easting) errors.push('UTM Easting is required');
                        if (!data.utmCoordinates.northing) errors.push('UTM Northing is required');
                        break;
                    case 'geological':
                        if (!data.wellName) errors.push('Well name is required');
                        if (!data.productionType) errors.push('Production type is required');
                        break;
                    case 'location':
                        if (!data.wellNumber) errors.push('Well number is required');
                        if (!data.field) errors.push('Field is required');
                        if (!data.objective) errors.push('Well objective is required');
                        break;
                    case 'prognosis':
                        if (!data.wellName) errors.push('Well name is required');
                        if (!data.rigName) errors.push('Rig name is required');
                        if (!data.wellType) errors.push('Well type is required');
                        break;
                    case 'completion':
                        if (!data.well) errors.push('Well is required');
                        if (!data.rig) errors.push('Rig is required');
                        if (!data.completionType) errors.push('Completion type is required');
                        break;
                    case 'tubular':
                        if (!data.wellNumber) errors.push('Well number is required');
                        if (!data.field) errors.push('Field is required');
                        break;
                    case 'wellInfo':
                        if (!data.well) errors.push('Well is required');
                        if (!data.rig) errors.push('Rig is required');
                        if (!data.wellType) errors.push('Well type is required');
                        break;
                    case 'wellhead':
                        if (!data.wellNumber) errors.push('Well number is required');
                        if (!data.serviceLocation) errors.push('Service location is required');
                        break;
                }

                setValidationErrors(errors);
                return errors.length === 0;
            };

            const handleSubmit = () => {
                if (validatePageData()) {
                    onSubmit(pageId, data);
                    setIsReviewing(false);
                    alert(`${pageId.charAt(0).toUpperCase() + pageId.slice(1)} data submitted successfully!`);
                }
            };

            const exportDocument = () => {
                if (pageType === 'PDF') {
                    exportToPDF();
                } else {
                    exportToExcel();
                }
            };

            const exportToPDF = () => {
                // Create a simple PDF export (in a real application, you'd use a proper PDF library)
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>${pageId.charAt(0).toUpperCase() + pageId.slice(1)} Document</title>
                            <style>
                                body { font-family: Arial, sans-serif; margin: 20px; }
                                h1 { color: #2c5364; }
                                .section { margin-bottom: 20px; }
                                .field { margin-bottom: 10px; }
                                .label { font-weight: bold; }
                            </style>
                        </head>
                        <body>
                            <h1>${pageId.charAt(0).toUpperCase() + pageId.slice(1)} Document</h1>
                            <div class="section">
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
            };

            const exportToExcel = () => {
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet([data]);
                XLSX.utils.book_append_sheet(wb, ws, pageId);
                XLSX.writeFile(wb, `${pageId}_document.xlsx`);
            };

            return (
                <div className="form-section">
                    <h3>📋 Review & Submit</h3>

                    {validationErrors.length > 0 && (
                        <div className="alert alert-warning">
                            <h4>⚠️ Please fix the following issues:</h4>
                            <ul>
                                {validationErrors.map((error, index) => (
                                    <li key={index}>{error}</li>
                                ))}
                            </ul>
                        </div>
                    )}

                    <div className="btn-group">
                        <button
                            className="btn btn-secondary"
                            onClick={() => setIsReviewing(!isReviewing)}
                        >
                            {isReviewing ? 'Hide Review' : 'Review Data'}
                        </button>

                        <button
                            className="btn btn-primary"
                            onClick={exportDocument}
                        >
                            📄 Export {pageType}
                        </button>

                        <button
                            className="btn btn-success"
                            onClick={handleSubmit}
                        >
                            ✅ Submit Page
                        </button>
                    </div>

                    {isReviewing && (
                        <div className="final-review">
                            <h4>Data Review</h4>
                            <pre style={{
                                background: '#f8f9fa',
                                padding: '15px',
                                borderRadius: '6px',
                                fontSize: '12px',
                                overflow: 'auto',
                                maxHeight: '300px'
                            }}>
                                {JSON.stringify(data, null, 2)}
                            </pre>
                        </div>
                    )}
                </div>
            );
        }

        // Render the application
        ReactDOM.render(<WellDocumentationSystem />, document.getElementById('root'));
    </script>
</body>
</html>